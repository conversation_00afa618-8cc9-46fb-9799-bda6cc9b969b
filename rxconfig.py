import reflex as rx
import os

# Get environment-specific URLs
def get_api_url():
    """Get API URL based on environment."""
    if os.getenv("RAILWAY_ENVIRONMENT_NAME"):  # Railway deployment
        base_url = os.getenv("BASE_URL", "https://talentapp-production.up.railway.app")
        return base_url
    return os.getenv("API_URL", "http://localhost:8000")

def get_websocket_url():
    """Get WebSocket URL based on environment."""
    if os.getenv("RAILWAY_ENVIRONMENT_NAME"):  # Railway deployment
        base_url = os.getenv("BASE_URL", "https://talentapp-production.up.railway.app")
        return base_url.replace("https://", "wss://")
    return os.getenv("WEBSOCKET_URL", "ws://localhost:8000")

config = rx.Config(
    app_name="Reflex_Chat",
    state_manager_lock_expiration=30000,  # 30 seconds lock expiration
    frontend_port=int(os.getenv("FRONTEND_PORT", "3000")),
    backend_port=int(os.getenv("BACKEND_PORT", "8000")),
    api_url=get_api_url(),
    websocket_url=get_websocket_url(),
)