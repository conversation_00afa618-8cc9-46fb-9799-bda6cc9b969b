#!/usr/bin/env python3
"""
Simple test server to verify port binding and basic HTTP response
This helps diagnose if the issue is with Reflex or basic networking
"""

import os
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            "status": "ok",
            "message": "Test server is working",
            "environment": {
                "RAILWAY_ENVIRONMENT_NAME": os.getenv("RAILWAY_ENVIRONMENT_NAME"),
                "BASE_URL": os.getenv("BASE_URL"),
                "FRONTEND_PORT": os.getenv("FRONTEND_PORT"),
                "BACKEND_PORT": os.getenv("BACKEND_PORT"),
                "HOST": os.getenv("HOST"),
                "DATABASE_URL_SET": bool(os.getenv("DATABASE_URL")),
            },
            "server_info": {
                "host": "0.0.0.0",
                "port": 3000,
                "python_version": sys.version,
            }
        }
        
        self.wfile.write(json.dumps(response, indent=2).encode())
    
    def log_message(self, format, *args):
        print(f"[TEST SERVER] {format % args}")

def run_test_server():
    host = "0.0.0.0"
    port = 3000
    
    print(f"🧪 Starting test server on {host}:{port}")
    print(f"Environment: {os.getenv('RAILWAY_ENVIRONMENT_NAME', 'local')}")
    print(f"Base URL: {os.getenv('BASE_URL', 'not set')}")
    
    server = HTTPServer((host, port), TestHandler)
    
    try:
        print(f"✅ Test server running at http://{host}:{port}")
        print("Press Ctrl+C to stop")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Test server stopped")
        server.server_close()

if __name__ == "__main__":
    run_test_server()
