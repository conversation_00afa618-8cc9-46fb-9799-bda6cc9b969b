import reflex as rx

def fixed_response_badge() -> rx.Component:
    """
    Create badges for response counts with fixed values.
    This is a temporary solution until we can fix the type conversion issues.

    Returns:
        Component with badges for each response type
    """
    return rx.hstack(
        rx.badge(
            rx.hstack(
                rx.text("Sí: "),
                rx.text("0"),
            ),
            color_scheme="green",
            variant="soft",
            size="1",
        ),
        rx.badge(
            rx.hstack(
                rx.text("No: "),
                rx.text("0"),
            ),
            color_scheme="red",
            variant="soft",
            size="1",
        ),
        rx.badge(
            rx.hstack(
                rx.text("A veces: "),
                rx.text("0"),
            ),
            color_scheme="yellow",
            variant="soft",
            size="1",
        ),
        spacing="2",
    )
