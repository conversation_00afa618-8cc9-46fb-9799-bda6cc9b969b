import reflex as rx
from Reflex_Chat.state import QA, ChatState
from Reflex_Chat.components.loading_icon import loading_icon
from Reflex_Chat.components.background import background_v3  # Import background component

message_style = rx.Style(
    display="inline-block",
    padding="1em",
    border_radius="15px",
    max_width=["30em", "30em", "50em", "50em", "50em", "50em"]
)

def message(qa: QA) -> rx.Component:
    """A single question/answer message.

    Args:
        qa: The question/answer pair.

    Returns:
        A component displaying the question/answer pair.
    """
    return rx.box(
        rx.box(
            rx.markdown(
                qa.question,
                background_color=rx.color("mauve", 3),
                color=rx.color("mauve", 12),
                style=message_style,
            ),
            text_align="right",
            margin_top="1em",
            margin_left="auto",  # Push to the right side
            margin_right="0",
        ),
        rx.box(
            rx.markdown(
                qa.answer,
                background_color=rx.color("accent", 3),
                color=rx.color("accent", 11),
                style=message_style,
            ),
            text_align="left",
            padding_top="1em",
            margin_left="0",
            margin_right="auto",  # Push to the left side
        ),
        width="100%",
        display="flex",
        flex_direction="column",
        align_items="center",
    )

def chat() -> rx.Component:
    """List all the messages in a single conversation."""
    return rx.box(
        rx.center(
            rx.vstack(
                rx.box(rx.foreach(ChatState.chats[ChatState.current_chat], message), width="100%"),
                py="8",
                width="100%",
                max_width="50em",
                padding_x="4px",
                padding_bottom="5em",
            ),
            width="100%",
        ),
        flex="1",
        overflow_y="auto",
        height="calc(100vh - 120px)",  # Adjust for navbar and action bar height
    )

def action_bar() -> rx.Component:
    """The action bar to send a new message."""
    return rx.center(
        rx.vstack(
            rx.form(
                rx.hstack(
                    rx.input(
                        rx.input.slot(
                            rx.tooltip(
                                rx.icon(tag="info", size=18),
                                content="Haz una pregunta sobre los resultados de las evaluaciones.",
                            )
                        ),
                        placeholder="Hazme una pregunta...",
                        id="question",
                        width=["15em", "20em", "45em", "50em", "50em", "50em"],
                    ),
                    rx.button(
                        rx.cond(
                            ChatState.processing,
                            loading_icon(height="1em"),
                            rx.text("Envia"),
                        ),
                        type="submit",
                    ),
                    align_items="center",
                ),
                on_submit=ChatState.process_question,
                reset_on_submit=True,
            ),
            rx.text(
                "El chatbot puede devolver respuestas incorrectas o engañosas. Usa el criterio.",
                text_align="center",
                font_size=".75em",
                color=rx.color("mauve", 10),
            ),
            align_items="center",
        ),
        position="sticky",
        bottom="0",
        left="0",
        padding_y="16px",
        backdrop_filter="auto",
        backdrop_blur="lg",
        border_top=f"1px solid {rx.color('mauve', 3)}",
        background_color=rx.color("mauve", 1),
        align_items="stretch",
        width="100%",
    )
