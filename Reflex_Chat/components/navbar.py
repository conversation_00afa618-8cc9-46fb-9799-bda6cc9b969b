import reflex as rx
from Reflex_Chat.state import AuthState, ChatState
from Reflex_Chat.components.modal import modal

def sidebar_chat(chat: str) -> rx.Component:
    """A sidebar chat item."""
    return rx.drawer.close(
        rx.hstack(
            rx.button(chat, on_click=lambda: ChatState.set_chat(chat), width="80%", variant="surface"),
            rx.button(
                rx.icon(tag="trash", stroke_width=1),
                on_click=ChatState.delete_chat,
                width="20%",
                variant="surface",
                color_scheme="red",
            ),
            width="100%",
        )
    )

def sidebar(trigger) -> rx.Component:
    """The sidebar component."""
    return rx.drawer.root(
        rx.drawer.trigger(trigger),
        rx.drawer.overlay(),
        rx.drawer.portal(
            rx.drawer.content(
                rx.vstack(
                    rx.heading("Chats", color=rx.color("mauve", 11)),
                    rx.divider(),
                    rx.foreach(ChatState.chat_titles, lambda chat: sidebar_chat(chat)),
                    align_items="stretch",
                    width="100%",
                ),
                top="auto",
                right="auto",
                height="100%",
                width="20em",
                padding="2em",
                background_color=rx.color("mauve", 1),
                outline="none",
            )
        ),
        direction="left",
    )


def modal(trigger) -> rx.Component:
    """A modal to create a new chat."""
    return rx.dialog.root(
        rx.dialog.trigger(trigger),
        rx.dialog.content(
            rx.hstack(
                rx.input(
                    placeholder="Create a new chat",
                    on_blur=ChatState.set_new_chat_name,
                    width=["15em", "20em", "30em"],
                ),
                rx.dialog.close(
                    rx.button("Create Chat", on_click=ChatState.create_chat),
                ),
                background_color=rx.color("mauve", 1),
                spacing="2",
                width="100%",
            ),
        ),
    )

def user_menu() -> rx.Component:
    """Dropdown menu for user info, logout, and navigation."""
    return rx.menu.root(
        rx.menu.trigger(
            rx.button(
                rx.hstack(
                    rx.icon(tag="menu"),
                    rx.text(f"{AuthState.token.get('name', 'User')}", color=rx.color("mauve", 12)),
                ),
                variant="ghost",
                size="2",
            )
        ),
        rx.menu.content(
            rx.menu.item(rx.link("Home", href="/")),
            rx.menu.item(rx.link("Chat", href="/chat")),
            rx.menu.separator(),
            rx.menu.item(rx.button("Logout", on_click=rx.redirect("/logout"), color_scheme="red")),
            background_color=rx.color("mauve", 1),
            shadow="md",
        ),
    )

def navbar_link(content: rx.Component, url: str) -> rx.Component:
    """Reusable navbar link component."""
    return rx.link(
        content,
        href=url
    )

def navbar() -> rx.Component:
    """Optimized Navbar with full-width coverage, chat buttons, and no horizontal scrolling."""
    return rx.box(
        rx.desktop_only(
            rx.flex(
                # Left Section: Logo & App Name
                rx.hstack(
                    rx.icon(
                        tag="superscript",
                        width="2.25em",
                        height="auto",
                        border_radius="25%",
                    ),
                    navbar_link(rx.text("Career Coach", size="7", weight="bold", color=rx.color("mauve", 12)), "/"),
                    align_items="center",
                    spacing="3",
                    padding_x="9",
                    flex="1",
                ),

                # Center Section: Navigation Links + Chat Navigation
                rx.hstack(
                    rx.button(
                        rx.text("Home", size="4", weight="medium", color=rx.color("mauve", 12)),
                        on_click=rx.redirect("/"),
                        variant="ghost",
                    ),
                    rx.button(
                        rx.text("Dashboard", size="4", weight="medium", color=rx.color("mauve", 12)),
                        on_click=rx.redirect("/dashboard"),
                        variant="ghost",
                    ),
                    rx.button(
                        rx.text("Evaluaciones", size="4", weight="medium", color=rx.color("mauve", 12)),
                        on_click=rx.redirect("/evaluations/performance"),
                        variant="ghost",
                    ),
                    rx.button(
                        rx.text("Chat", size="4", weight="medium", color=rx.color("mauve", 12)),
                        on_click=rx.redirect("/chat"),
                        variant="ghost",
                    ),
                    spacing="5",
                    justify="center",
                    flex="1",
                ),
                rx.hstack(
                    rx.button("+ New Chat", size="2"),
                    rx.button(
                        rx.icon(tag="messages-square", color=rx.color("mauve", 12)),
                        background_color=rx.color("mauve", 6),
                        size="2",
                    ),
                    rx.color_mode.button(size="2", border_radius="full"),  # Night & Day switcher
                    rx.menu.root(
                        rx.menu.trigger(
                            rx.icon_button(
                                rx.icon("user"),
                                size="2",
                                radius="full",
                            )
                        ),
                        rx.menu.content(
                            rx.menu.item("Settings"),
                            rx.menu.separator(),
                            rx.menu.item(rx.button("Log out", on_click=rx.redirect("/logout")), width="100%"),
                        ),
                        justify="end",
                    ),
                    spacing="2",
                    align_items="center",
                    justify="end",
                    padding_x="9",
                    flex="1",
                ),
                justify="between",  # FIXED: Use 'between' instead of 'space-between'
                align="center",
                width="100%",
                max_width="96vw",
                height="4rem",
                padding_y="2",
                margin_x="auto",
                background_color="mauve.12",
                overflow="hidden",
                padding_x="9",
                gap="6",
            )
        ),
        width="100%",
        background=rx.color("mauve", 1),
        position="sticky",
        top="0",
        z_index="50",
        margin="0",
        padding="0",
    )