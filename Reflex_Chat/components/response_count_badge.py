import reflex as rx
from typing import Dict

def response_count_badge(response_counts: Dict[str, int]) -> rx.Component:
    """
    Create badges for response counts.

    Args:
        response_counts: Dictionary with response counts

    Returns:
        Component with badges for each response type
    """
    # Extract counts safely
    try:
        # Try to convert to a regular Python dict if it's a reactive var
        if hasattr(response_counts, "to_py"):
            counts_dict = response_counts.to_py()
        else:
            counts_dict = response_counts if isinstance(response_counts, dict) else {}

        # Get counts with fallbacks
        si_count = counts_dict.get("si", 0) if counts_dict else 0
        no_count = counts_dict.get("no", 0) if counts_dict else 0
        a_veces_count = counts_dict.get("a_veces", 0) if counts_dict else 0
    except Exception:
        # If any error occurs, use default values
        si_count = 0
        no_count = 0
        a_veces_count = 0

    return rx.hstack(
        # Sí badge
        rx.badge(
            rx.hstack(
                rx.text("Sí: "),
                rx.text(si_count),
            ),
            color_scheme="green",
            variant="soft",
            size="1",
        ),
        # No badge
        rx.badge(
            rx.hstack(
                rx.text("No: "),
                rx.text(no_count),
            ),
            color_scheme="red",
            variant="soft",
            size="1",
        ),
        # A veces badge
        rx.badge(
            rx.hstack(
                rx.text("A veces: "),
                rx.text(a_veces_count),
            ),
            color_scheme="yellow",
            variant="soft",
            size="1",
        ),
        spacing="2",
    )
