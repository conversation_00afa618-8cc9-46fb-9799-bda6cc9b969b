from sqlmodel import SQLModel, create_engine, Session
import os
from sqlalchemy import text

# Import the models so SQLModel knows about them
from Reflex_Chat.database.models import *

# Get individual database parameters
DB_USER = os.getenv("POSTGRES_USER", "postgres")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")
DB_NAME = os.getenv("POSTGRES_DB", "postgres")
DB_HOST = "postgres"  # Use the Docker service name
DB_PORT = "5432"

# Construct DATABASE_URL manually
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
print(f"Connecting to database with URL: {DATABASE_URL}")

# Create the SQLAlchemy engine with additional configuration
engine = create_engine(
    DATABASE_URL,
    echo=True,  # echo=True shows SQL logs
    pool_pre_ping=True,  # Enable connection health checks
    pool_recycle=3600,  # Recycle connections after 1 hour
)

# Session generator
def get_session():
    return Session(engine)

# For dev: create all tables
def create_db_and_tables():
    SQLModel.metadata.create_all(engine)

# Drop and recreate all tables (WARNING: This deletes all data)
def reset_db_and_tables():
    print("Dropping all tables and types...")
    
    # Use a raw connection to execute SQL directly with CASCADE option
    with engine.begin() as conn:
        # First, explicitly drop the problematic table
        conn.execute(text("DROP TABLE IF EXISTS evaluationscore CASCADE"))
        
        # Then drop all remaining tables in the public schema
        conn.execute(text("""
            DO $$ DECLARE
                r RECORD;
            BEGIN
                FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
                    EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
                END LOOP;
            END $$;
        """))
        
        # Drop all enum types
        conn.execute(text("DROP TYPE IF EXISTS competencycategory CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluationtype CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluatortype CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluationstatus CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS projectstatus CASCADE"))
    
    print("Creating all tables and types...")
    SQLModel.metadata.create_all(engine)
    print("Database reset complete.")
