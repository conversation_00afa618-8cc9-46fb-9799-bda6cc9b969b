"""
Score Calculator Module
======================

This module handles all score calculations for the evaluation system. It works at three levels:

1. Competency Scores
   - Basic unit of measurement
   - Calculated based on behavior type of each question:
     - EXPERT: Sí = 2 points, No = 0 points, A veces = 1 point
     - TALENTED: Sí = 3 points, No = 0 points, A veces = 1.5 points
     - LOW_SKILL: Sí = -2 points, No = 0 points, A veces = -1 point
   - Final score = (total points gained / total possible points for ideal behavior) * 100
   - Includes individual answer comments

2. Factor Scores
   - Groups related competencies
   - Calculated as simple average of competency scores (equal weights)
   - Each competency has equal importance regardless of number of questions
   - Aggregates comments from competencies

3. Category Scores
   - Top-level grouping
   - Calculated as simple average of competency scores (equal weights)
   - Direct calculation from competencies, not through factors
   - Aggregates comments from competencies

Important Notes:
- All calculations work at single evaluation level
- Scores range from 0 to 100
- Comments are preserved and aggregated at each level
- Use recalculate_all_scores() for system-wide score refresh

Usage:
    from Reflex_Chat.database.utils.score_calculator import calculate_competency_scores

    # Calculate scores for a single evaluation
    scores = calculate_competency_scores(evaluation_id, session)
"""

from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlmodel import Session, select
from Reflex_Chat.database.models import (
    Evaluation, EvaluationAnswer, CompetencyScore,
    CategoryScore, FactorScore, Competency, User,
    EvaluationQuestion, CompetencyCategory, ResponseType, BehaviorType
)

def calculate_competency_scores(evaluation_id: int, session: Session) -> List[CompetencyScore]:
    """
    Calculate competency scores for a single evaluation.
    Score is calculated based on behavior type of each question:
    - EXPERT: Sí = 2 points, No = 0 points, A veces = 1 point
    - TALENTED: Sí = 3 points, No = 0 points, A veces = 1.5 points
    - LOW_SKILL: Sí = -2 points, No = 0 points, A veces = -1 point

    Final score = (total points gained / total possible points for ideal behavior) * 100
    Includes comments from individual answers.
    """
    evaluation = session.get(Evaluation, evaluation_id)
    if not evaluation:
        raise ValueError(f"Evaluation {evaluation_id} not found")

    # Group answers by competency
    answers = session.exec(
        select(EvaluationAnswer)
        .where(EvaluationAnswer.evaluation_id == evaluation_id)
    ).all()

    competency_answers: Dict[int, List[Tuple[EvaluationAnswer, EvaluationQuestion, str]]] = {}
    for answer in answers:
        question = session.get(EvaluationQuestion, answer.question_id)
        if question:
            if question.competency_id not in competency_answers:
                competency_answers[question.competency_id] = []
            competency_answers[question.competency_id].append((answer, question, answer.comment or ""))

    # Calculate scores for each competency based on behavior type
    scores = []
    for competency_id, comp_answers in competency_answers.items():
        total_points = 0
        total_possible_points = 0

        for answer, question, _ in comp_answers:
            # Determine points based on behavior type and response
            if question.behavior_type == BehaviorType.EXPERT:
                if answer.response == ResponseType.SI:  # Sí
                    total_points += 2
                elif answer.response == ResponseType.A_VECES:  # A veces
                    total_points += 1
                # No = 0 points
                total_possible_points += 2  # Max possible for expert behavior

            elif question.behavior_type == BehaviorType.TALENTED:
                if answer.response == ResponseType.SI:  # Sí
                    total_points += 3
                elif answer.response == ResponseType.A_VECES:  # A veces
                    total_points += 1.5
                # No = 0 points
                total_possible_points += 3  # Max possible for talented behavior

            elif question.behavior_type == BehaviorType.LOW_SKILL:
                if answer.response == ResponseType.SI:  # Sí
                    total_points -= 2  # Negative points for low skill behaviors
                elif answer.response == ResponseType.A_VECES:  # A veces
                    total_points -= 1  # Negative points for low skill behaviors
                # No = 0 points (best for low skill behaviors)
                total_possible_points += 0  # No points possible for low skill (ideal is 0)

        # Calculate final score as percentage of possible points
        # For low_skill behaviors, not having them is the ideal (0 points)
        score = (total_points / total_possible_points) * 100 if total_possible_points > 0 else 0

        # Ensure score is between 0 and 100
        score = max(0, min(100, score))

        comments = "\n".join(f"- {comment}" for _, _, comment in comp_answers if comment)

        eval_score = CompetencyScore(
            evaluation_id=evaluation_id,
            competency_id=competency_id,
            score=score,
            comments=comments if comments else None,
            created_at=datetime.utcnow()
        )
        scores.append(eval_score)

    return scores

def calculate_factor_scores_for_evaluation(evaluation_id: int, session: Session) -> List[FactorScore]:
    """
    Calculate factor-level scores for a single evaluation.
    Includes aggregated comments from competency scores.
    """
    # Get competency scores and their related competencies in one query
    competency_scores = (
        session.exec(
            select(CompetencyScore, Competency)
            .join(Competency, CompetencyScore.competency_id == Competency.id)
            .where(CompetencyScore.evaluation_id == evaluation_id)
        ).all()
    )

    factor_data: Dict[Tuple[CompetencyCategory, str], List[Tuple[float, str]]] = {}
    for score, competency in competency_scores:
        if competency.factor:
            key = (competency.category, competency.factor)
            if key not in factor_data:
                factor_data[key] = []
            factor_data[key].append((score.score, score.comments or ""))

    return [
        FactorScore(
            evaluation_id=evaluation_id,
            category=category,
            factor=factor,
            score=sum(score for score, _ in scores) / len(scores) if scores else 0,
            comments="\n\n".join(f"Competency: {comment}" for _, comment in scores if comment),
            evaluation_count=len(scores),
            created_at=datetime.utcnow()
        )
        for (category, factor), scores in factor_data.items()
    ]

def calculate_category_scores_for_evaluation(evaluation_id: int, session: Session) -> List[CategoryScore]:
    """
    Calculate category-level scores for a single evaluation.
    Scores are calculated directly from competency scores.
    """
    # Get competency scores and their related competencies in one query
    competency_scores = (
        session.exec(
            select(CompetencyScore, Competency)
            .join(Competency, CompetencyScore.competency_id == Competency.id)
            .where(CompetencyScore.evaluation_id == evaluation_id)
        ).all()
    )

    category_data: Dict[CompetencyCategory, List[Tuple[float, str]]] = {}
    for score, competency in competency_scores:
        category = competency.category
        if category not in category_data:
            category_data[category] = []
        category_data[category].append((score.score, score.comments or ""))

    return [
        CategoryScore(
            evaluation_id=evaluation_id,
            category=category,
            score=sum(score for score, _ in scores) / len(scores) if scores else 0,
            comments="\n\n".join(f"Competency: {comment}" for _, comment in scores if comment),
            evaluation_count=len(scores),
            created_at=datetime.utcnow()
        )
        for category, scores in category_data.items()
    ]

def recalculate_all_scores(session: Session):
    """
    Recalculate all scores for all evaluations in the system.
    Used when score calculation logic changes or data needs refreshing.
    """
    # Clear existing scores
    session.exec("DELETE FROM competency_score")
    session.exec("DELETE FROM factor_score")
    session.exec("DELETE FROM category_score")
    session.commit()

    # Get all evaluations
    evaluations = session.exec(select(Evaluation)).all()

    # Recalculate scores for each evaluation
    for evaluation in evaluations:
        competency_scores = calculate_competency_scores(evaluation.id, session)
        factor_scores = calculate_factor_scores_for_evaluation(evaluation.id, session)
        category_scores = calculate_category_scores_for_evaluation(evaluation.id, session)
        session.add_all(competency_scores + factor_scores + category_scores)
    session.commit()
