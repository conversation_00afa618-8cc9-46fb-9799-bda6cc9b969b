import reflex as rx
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.components.background import background_v3
from Reflex_Chat.state import AuthState

@rx.page(route="/evaluations", title="Evaluations", on_load=AuthState.require_auth)
def evaluations_page() -> rx.Component:
    return rx.vstack(
        navbar(),
        background_v3(),
        rx.flex(
            rx.box(
                rx.vstack(
                    rx.heading("Evaluaciones", size="8", margin_top="6", text_align="center"),
                    rx.text("Selecciona el tipo de evaluación para comenzar", size="4", color="gray.600", text_align="center"),
                    rx.hstack(
                        rx.box(
                            rx.button(
                                rx.vstack(
                                    #rx.icon("star", size=6),
                                    rx.text("Evaluación de Desempeño", size="4", text_align="center"),
                                    spacing="4",
                                ),
                                on_click=rx.redirect("/evaluations/performance"),
                                size="3",
                                color_scheme="blue",
                                width="200px",
                                height="150px",
                                variant="outline",
                                _hover={"transform": "scale(1.05)", "transition": "all 0.2s"},
                                background=rx.color("mauve", 1),
                            ),
                            padding="4",
                        ),
                        rx.box(
                            rx.button(
                                rx.vstack(
                                    #rx.icon("chart", size=6),
                                    rx.text("Evaluación de Potencial", size="4", text_align="center"),
                                    spacing="4",
                                ),
                                on_click=rx.redirect("/evaluations/potential"),
                                size="3",
                                color_scheme="teal",
                                width="200px",
                                height="150px",
                                variant="outline",
                                _hover={"transform": "scale(1.05)", "transition": "all 0.2s"},
                                background=rx.color("mauve", 1),
                            ),
                            padding="4",
                        ),
                        spacing="8",
                    ),
                    spacing="6",
                    align="center",
                    padding="3",
                ),
                width="50%",
                height="40vh",
                max_width="600px",
                padding="6",
                flex_grow="1",
                display="flex",
                align_items="center",
                justify_content="center",
                border_radius="lg",
                shadow="md",
            ),
            width="100%",
            height="calc(100vh - 64px)",  # Subtract navbar height
            flex_grow="1",
            align="center",
            justify="center",
            background="gray.50",
            flex_direction="column",
        ),
        width="100%",
        height="70vh",
        spacing="0",
    )
