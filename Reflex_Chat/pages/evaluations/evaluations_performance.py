# Reflex_Chat/pages/evaluations/performance.py

import reflex as rx
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.state import PerformanceEvalState
from Reflex_Chat.components.background import background_v3


@rx.page(
    route="/evaluations/performance",
    title="Evaluaciónes",
    on_load=PerformanceEvalState.load_user_projects,
)
def performance_evaluations_page() -> rx.Component:
    def render_teammate(teammate, project):
        return rx.card(
            rx.flex(
                rx.text(teammate["name"]),
                rx.button(
                    "Evaluar",
                on_click=rx.redirect(
                    f"/evaluations/performance/start?project_id={project['id']}&evaluatee_id={teammate['id']}"
                ),
                size="2",
                color_scheme="blue",
            ),
            width="100%",
            justify="between",
            align_items="center",
            flex="1",
            spacing="5",
        ),
        width="100%",
        style={"transition": "all 0.2s ease"},
        _hover={"transform": "translateY(-3px)", "boxShadow": "0 4px 8px rgba(0, 0, 0, 0.1)"},
        )

    def render_project(project):
        return rx.vstack(
            rx.heading(project["name"], size="5"),
            rx.cond(
                project.self_evaluated,
                rx.text("Ya has completado tu auto evaluación.", color="gray"),
                rx.card(
                    rx.flex(
                        rx.text("Auto Evaluación"),
                        rx.button(
                            "Evaluar",
                            on_click=rx.redirect(
                                f"/evaluations/performance/start?project_id={project.id}&evaluatee_id={PerformanceEvalState.db_user_id}"
                            ),
                            size="2",
                            color_scheme="blue",
                        ),
                        width="100%",
                        justify="between",
                        align_items="center",
                        flex="1",
                        spacing="5",
                        mb="4",
                        style={"transition": "all 0.2s ease"},
                        _hover={"transform": "translateY(-3px)", "boxShadow": "0 4px 8px rgba(0, 0, 0, 0.1)"},
                    ),
                ),
            ),
            rx.cond(
                project.teammates.length() > 0,
                rx.vstack(
                    rx.foreach(project.teammates, lambda teammate: render_teammate(teammate, project)),
                    spacing="5",
                ),
                rx.text("Ya has evaluado a todos en este proyecto.", color="gray"),
            ),
            #rx.divider(my="4"),
            spacing="5",
            width="100%",
            align_items="stretch",
        )

    return rx.box(
        navbar(),
        rx.center(
            rx.container(
                rx.vstack(
                    rx.heading(
                        "Evaluaciones Pendientes",
                        size="6",
                        mb="8",
                        text_align="left",
                        background="linear-gradient(90deg, #1a365d 0%, #2a4365 100%)",
                        background_clip="text",
                        font_weight="bold",
                    ),
                    rx.divider(mb="6"),
                    rx.cond(
                        PerformanceEvalState.user_projects.length() == 0,
                        rx.box(
                            rx.icon(
                                tag="inbox",
                                mb="4",
                                font_size="32px",
                                color="gray.500",
                            ),
                            rx.text(
                                "No hay proyectos disponibles para evaluar.",
                                color="gray.500",
                                font_size="lg",
                            ),
                            text_align="center",
                            py="12",
                        ),
                        rx.flex(
                            rx.foreach(
                                PerformanceEvalState.user_projects,
                                render_project
                            ),
                            spacing="6",
                            justify="center",
                            width="100%",
                            direction="column",
                        ),
                    ),
                    width="100%",
                    spacing="6",
                    align_items="stretch",
                    padding="2%",
                ),
                width="80%",
                max_width="1000px",
                height="auto",
                max_height="80vh",
                overflow="auto",
                style={
                    "transition": "all 0.2s ease",
                    "scrollbarWidth": "thin",
                    "scrollbarColor": "rgba(155, 155, 155, 0.5) transparent",
                    "boxShadow": "0 4px 12px rgba(0, 0, 0, 0.1)"
                },
                _hover={},
            ),
            width="100%",
            height="calc(100vh - 4rem)",
            padding="2%",
        ),
        bg="gray.50",
        min_height="100vh",
        width="100%",
    )
