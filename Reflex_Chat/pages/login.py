import reflex as rx
from Reflex_Chat.state import AuthState

@rx.page(route="/login", title="Login")
def login_page() -> rx.Component:
    return rx.flex(
        rx.box(
            rx.vstack(
                rx.heading(
                    "Sign in with Microsoft",
                    size="6",
                    weight="bold",
                    text_align="center",
                    color="blue.600",
                ),
                rx.text(
                    "Access your account securely with Microsoft Single Sign-On.",
                    size="4",
                    color="gray.500",
                    text_align="center",
                ),

                # Login Button
                rx.button(
                    "Login with Microsoft",
                    on_click=AuthState.redirect_sso,
                    width="100%",
                    height="3rem",
                    font_size="4",
                    color_scheme="blue",
                    border_radius="md",
                    shadow="md",
                    _hover={"shadow": "lg"},
                    data_testid="login-button",
                ),

                rx.text("", id="error-message", color="red.500", margin_top="4", display="none"),
                spacing="6",
                width="100%",
                align="center",
            ),
            width="100%",
            max_width="400px",
            padding="6",
            flex_grow="1",
            display="flex",
            align_items="center",
            justify_content="center",
            border_radius="lg",
            shadow="md",
            background="white",
        ),

        # Ensure Reflex is ready before calling addEvents
        rx.script("""
        (function() {
            console.log("Login page loaded - resetting session storage");

            // Ensure storage is cleared before login
            sessionStorage.removeItem('auth_token');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_flow');

            //Wait for Reflex to be ready before calling addEvents
            function waitForReflex(callback, maxAttempts = 20) {
                let attempts = 0;
                const interval = setInterval(() => {
                    if (window._reflex) {
                        clearInterval(interval);
                        callback();
                    } else {
                        attempts++;
                        if (attempts >= maxAttempts) {
                            clearInterval(interval);
                            console.error("Reflex did not load in time.");
                        }
                    }
                }, 1000);
            }

            //Force a login event when button is clicked
            document.querySelector("[data-testid='login-button']").addEventListener("click", function() {
                console.log("Login button clicked - forcing login event");
                waitForReflex(() => {
                    window._reflex.addEvents([{ 
                        name: "Reflex_Chat.state.AuthState.redirect_sso", 
                        payload: {} 
                    }]);
                });
            });

            // Function to check for auth URI and redirect
            function checkRedirect() {
                const authUriElem = document.querySelector("[data-auth-uri]");
                if (!authUriElem) return;
                
                const authUri = authUriElem.getAttribute("data-auth-uri");
                if (authUri && authUri !== "") {
                    console.log("Redirecting to Microsoft login...");
                    window.location.href = authUri;
                }
            };

            setInterval(checkRedirect, 1000); // Check every 1000ms
        })();
        """),

        # Hidden element to store auth URI
        rx.box(
            data_auth_uri=AuthState.auth_uri,
            width="0",
            height="0",
            display="none",
        ),
        width="100vw",
        height="100vh",
        min_height="100vh",
        flex_grow="1",
        align="center",
        justify="center",  # ✅ THIS IS WHAT MAKES IT VERTICALLY CENTERED
        background="gray.50",
        flex_direction="column",  # ✅ Ensures proper vertical stacking
    )
