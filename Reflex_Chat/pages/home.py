import reflex as rx
from Reflex_Chat.state import AuthState
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.components.background import background_v3  # Import background component

@rx.page(route="/", on_load=AuthState.require_auth)
def home_page() -> rx.Component:
    """Home Page with a button to access chat."""
    return rx.box(
        rx.cond(
            AuthState.check_auth,
            rx.box(
                navbar(),  # Include the navbar for navigation
                background_v3(),

                # Centered content
                rx.center(
                    rx.vstack(
                        rx.heading(f"Bienvenido, {AuthState.token.get('name', 'User')}!", size="8", weight="bold", z_index="10", color="mauve.12", text_align="center", letter_spacing="tight", text_shadow="1px 1px 3px rgba(0, 0, 0, 0.2)"),
                        rx.text("Empieza a chatear ahora!", size="4", color="gray.300"),
                        rx.button("Go to Chat", on_click=rx.redirect("/chat"), color_scheme="blue", size="3"),
                        spacing="4",
                        align="center",
                    ),
                    height="70vh",  # Makes sure content is vertically centered
                    width="100%",
                ),
            ),
            rx.fragment(
                rx.text("Redirecting to login..."),
                rx.script("setTimeout(() => { window.location.href = '/login'; }, 1000);")
            )
        ),
    align_items="stretch",
    width="100%",
    spacing="0",
    margin="0",
    padding="0",
    )
