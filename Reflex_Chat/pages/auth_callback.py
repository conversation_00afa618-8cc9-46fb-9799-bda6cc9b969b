import reflex as rx
from Reflex_Chat.state import AuthState

@rx.page(route="/auth/callback", on_load=AuthState.process_auth_code)
def callback_page() -> rx.Component:
    """Handles OAuth callback and processes authentication."""
    return rx.container(
        rx.center(
            rx.vstack(
                rx.spinner(size="3"),
                rx.text("Processing login... Please wait."),
                spacing="4",
                width="100%",
                max_width="400px",
                padding="4",
            ),
            padding_y="20",
            width="100%",
        )
    )