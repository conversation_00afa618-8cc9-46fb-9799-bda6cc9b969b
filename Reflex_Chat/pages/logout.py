import reflex as rx
from Reflex_Chat.state import AuthState, AUTHORITY

@rx.page(route="/logout", on_load=AuthState.logout)
def logout_page() -> rx.Component:
    """Handles user logout."""
    return rx.container(
        rx.text("Logging out... Redirecting..."),
        rx.script(f"""
        (function() {{
            console.log("Clearing auth storage and logging out...");
            localStorage.removeItem('auth_token');
            sessionStorage.removeItem('auth_token');
            localStorage.clear();
            sessionStorage.clear();

            // Redirect to Microsoft's logout page, then back to login
            setTimeout(() => {{
                window.location.href = '{AUTHORITY}/oauth2/v2.0/logout?post_logout_redirect_uri=http://localhost:3000/login';
            }}, 500);
        }})();
        """)
    )
