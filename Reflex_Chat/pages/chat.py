import reflex as rx
from Reflex_Chat.state import AuthState
from Reflex_Chat.components.navbar import navbar
from Reflex_Chat.components.chat import chat, action_bar

@rx.page(route="/chat", on_load=AuthState.require_auth)
def chat_page() -> rx.Component:
    """Chat Page where users can send messages."""
    return rx.flex(
        rx.vstack(
            navbar(),    # Include navbar at the top
            rx.flex(
                chat(),      # Chat window
                action_bar(),# Input bar
                direction="column",
                flex="1",
                overflow="hidden",
            ),
            background_color=rx.color("mauve", 1),
            color=rx.color("mauve", 12),
            min_height="100vh",
            height="100vh",
            align_items="stretch",
            spacing="0",
            overflow="hidden",
        ),
        direction="column",
        width="100%",
        height="100vh",
        overflow="hidden",
    )
