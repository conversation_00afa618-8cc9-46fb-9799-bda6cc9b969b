from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from sqlmodel import Session
from typing import List, Optional
from pydantic import BaseModel

from Reflex_Chat.api.services.langgraph_rag_service import LangGraphRAGService
from Reflex_Chat.database.db import get_session

app = APIRouter(prefix="/api/rag")

class ConversationItem(BaseModel):
    question: str
    answer: str

class QuestionRequest(BaseModel):
    question: str
    conversation_history: Optional[List[ConversationItem]] = None
    user_name: Optional[str] = None
    user_role: Optional[str] = None
    user_id: Optional[int] = None

class QuestionResponse(BaseModel):
    answer: str

@app.post("/question")
async def process_question(
    request: QuestionRequest,
    db: Session = Depends(get_session)
):
    """Process a question using RAG with Azure search and OpenAI with non-streaming response."""
    rag_service = LangGraphRAGService(db)

    history = None
    if request.conversation_history:
        history = []
        for item in request.conversation_history:
            history.append({"role": "user", "content": item.question})
            history.append({"role": "assistant", "content": item.answer})

    answer = await rag_service.process_question(
        question=request.question,
        conversation_history=history,
        user_name=request.user_name,
        user_role=request.user_role,
        user_id=request.user_id
    )
    return {"answer": answer}

@app.post("/question/stream")
async def process_question_stream(
    request: QuestionRequest,
    db: Session = Depends(get_session)
):
    """Process a question using LangGraph RAG with Azure search and OpenAI with streaming response."""
    rag_service = LangGraphRAGService(db)

    history = None
    if request.conversation_history:
        history = []
        for item in request.conversation_history:
            history.append({"role": "user", "content": item.question})
            history.append({"role": "assistant", "content": item.answer})

    async def generate_stream():
        async for chunk in rag_service.process_question_stream(
            question=request.question,
            conversation_history=history,
            user_name=request.user_name,
            user_role=request.user_role,
            user_id=request.user_id
        ):
            yield chunk

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream"
    )
