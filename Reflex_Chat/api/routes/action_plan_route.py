from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import BaseModel

from Reflex_Chat.api.services.action_plan_service import ActionPlanService
from Reflex_Chat.database.db import get_session
from Reflex_Chat.database.models import CompetencyCategory

app = APIRouter(prefix="/api/action-plans")

class CreateActionPlanRequest(BaseModel):
    evaluation_id: int
    category: str
    factor: str
    action_plan_text: str
    evaluator_id: int

class UpdateActionPlanRequest(BaseModel):
    action_plan_text: str
    evaluator_id: int

@app.post("/")
async def create_action_plan(
    request: CreateActionPlanRequest,
    db: Session = Depends(get_session)
) -> Dict[str, Any]:
    """Create a new action plan for a specific factor in an evaluation."""
    action_plan_service = ActionPlanService(db)
    
    # Convert category string to enum
    try:
        category_enum = CompetencyCategory(request.category.lower())
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid category: {request.category}")
    
    action_plan = action_plan_service.create_action_plan(
        evaluation_id=request.evaluation_id,
        category=category_enum,
        factor=request.factor,
        action_plan_text=request.action_plan_text,
        evaluator_id=request.evaluator_id
    )
    
    if not action_plan:
        raise HTTPException(status_code=403, detail="Not authorized to create action plans or evaluation not found")
    
    return {
        "id": action_plan.id,
        "evaluation_id": action_plan.evaluation_id,
        "category": action_plan.category.value,
        "factor": action_plan.factor,
        "action_plan_text": action_plan.action_plan_text,
        "evaluator_id": action_plan.evaluator_id,
        "created_at": action_plan.created_at.isoformat() if action_plan.created_at else None,
        "updated_at": action_plan.updated_at.isoformat() if action_plan.updated_at else None
    }

@app.get("/evaluation/{evaluation_id}")
async def get_action_plans_for_evaluation(
    evaluation_id: int,
    db: Session = Depends(get_session)
) -> List[Dict[str, Any]]:
    """Get all action plans for a specific evaluation."""
    action_plan_service = ActionPlanService(db)
    action_plans = action_plan_service.get_action_plans_for_evaluation(evaluation_id)
    return action_plans

@app.get("/user/{user_id}")
async def get_action_plans_for_user(
    user_id: int,
    project_id: Optional[int] = None,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    db: Session = Depends(get_session)
) -> Dict[str, List[Dict[str, Any]]]:
    """Get all action plans for a specific user (evaluatee), grouped by factor."""
    action_plan_service = ActionPlanService(db)
    action_plans = action_plan_service.get_action_plans_for_user(
        user_id=user_id,
        project_id=project_id,
        start_date=start_date,
        end_date=end_date
    )
    return action_plans

@app.put("/{action_plan_id}")
async def update_action_plan(
    action_plan_id: int,
    request: UpdateActionPlanRequest,
    db: Session = Depends(get_session)
) -> Dict[str, Any]:
    """Update an existing action plan."""
    action_plan_service = ActionPlanService(db)
    
    action_plan = action_plan_service.update_action_plan(
        action_plan_id=action_plan_id,
        action_plan_text=request.action_plan_text,
        evaluator_id=request.evaluator_id
    )
    
    if not action_plan:
        raise HTTPException(status_code=404, detail="Action plan not found or not authorized")
    
    return {
        "id": action_plan.id,
        "evaluation_id": action_plan.evaluation_id,
        "category": action_plan.category.value,
        "factor": action_plan.factor,
        "action_plan_text": action_plan.action_plan_text,
        "evaluator_id": action_plan.evaluator_id,
        "created_at": action_plan.created_at.isoformat() if action_plan.created_at else None,
        "updated_at": action_plan.updated_at.isoformat() if action_plan.updated_at else None
    }

@app.delete("/{action_plan_id}")
async def delete_action_plan(
    action_plan_id: int,
    evaluator_id: int = Query(..., description="ID of the evaluator requesting deletion"),
    db: Session = Depends(get_session)
) -> Dict[str, str]:
    """Delete an action plan."""
    action_plan_service = ActionPlanService(db)
    
    success = action_plan_service.delete_action_plan(
        action_plan_id=action_plan_id,
        evaluator_id=evaluator_id
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="Action plan not found or not authorized")
    
    return {"message": "Action plan deleted successfully"}
