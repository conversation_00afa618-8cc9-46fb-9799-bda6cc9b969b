
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session
from typing import Dict, Any

from Reflex_Chat.api.services.user_summary_service import UserSummaryService
from Reflex_Chat.database.db import get_session

app = APIRouter(prefix="/api/users")

@app.get("/{user_id}/user_role_project_summary")
async def get_user_summary_data(user_id: int, db: Session = Depends(get_session)) -> Dict[str, Any]:
    """Get user dashboard data including basic info, role, and active projects."""
    user_summary_service = UserSummaryService(db)
    data = user_summary_service.get_user_summary_data(user_id)
    
    if not data:
        raise HTTPException(status_code=404, detail="User not found")
    
    return data
    



