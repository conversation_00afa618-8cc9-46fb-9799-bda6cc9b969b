from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from typing import Dict, Any, Optional
from datetime import datetime

from Reflex_Chat.api.services.score_calculator_service import UserScoreService
from Reflex_Chat.database.db import get_session

app = APIRouter(prefix="/api/users")

@app.get("/{user_id}/evaluation_score_calculator")
async def get_user_score_calculation_data(
    user_id: int,
    project_id: Optional[int] = None,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    evaluator_role: Optional[str] = None,
    role_weights: Optional[Dict[str, float]] = None,
    category_weights: Optional[Dict[str, float]] = None,
    db: Session = Depends(get_session)
) -> Dict[str, Any]:
    """
    Get user score calculator data with optional filters and weights.

    Role weights and category weights should sum to 1.0 if provided.
    If not provided, default weights will be used and adjusted based on available data.
    """
    import logging
    logger = logging.getLogger("service.user_score")
    logger.info(f"API route called with user_id={user_id}, project_id={project_id}, evaluator_role={evaluator_role}")
    logger.info(f"Using role_weights: {role_weights}, category_weights: {category_weights}")

    score_calculator_service = UserScoreService(db)

    # Validate weights if provided
    if role_weights and abs(sum(role_weights.values()) - 1.0) > 0.001:
        raise HTTPException(status_code=400, detail="Role weights must sum to 1.0")
    if category_weights and abs(sum(category_weights.values()) - 1.0) > 0.001:
        raise HTTPException(status_code=400, detail="Category weights must sum to 1.0")

    # Get the overall score with weights
    overall_score = score_calculator_service.get_overall_score(
        user_id,
        role_weights,
        category_weights,
        project_id,
        start_date,
        end_date,
        evaluator_role
    )

    # Get other scores
    data = {
        "overall_score": overall_score,
        "competency_scores": score_calculator_service.get_competency_scores(
            user_id, project_id, start_date, end_date, role_weights, evaluator_role
        ),
        "factor_scores": score_calculator_service.get_factor_scores(
            user_id, project_id, start_date, end_date, role_weights, evaluator_role
        ),
        "category_scores": score_calculator_service.get_category_scores(
            user_id, project_id, start_date, end_date, role_weights, evaluator_role
        )
    }

    if not data:
        raise HTTPException(status_code=404, detail="Score data not found")

    return data

@app.get("/{user_id}/competency_questions/{competency_id}")
async def get_competency_questions_by_behavior(
    user_id: int,
    competency_id: int,
    project_id: Optional[int] = None,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    evaluator_role: Optional[str] = None,
    db: Session = Depends(get_session)
) -> Dict[str, Any]:
    """
    Get competency questions grouped by behavior type with response counts.

    Returns a dictionary with:
    - competency_id: The ID of the competency
    - questions_by_behavior: Dictionary with behavior types as keys and lists of questions as values
      Each question includes:
      - id: Question ID
      - text: Question text
      - response_counts: Dictionary with response types as keys and counts as values
    """
    score_calculator_service = UserScoreService(db)

    data = score_calculator_service.get_competency_questions_by_behavior(
        user_id,
        competency_id,
        project_id,
        start_date,
        end_date,
        evaluator_role
    )

    if not data or not data.get("questions_by_behavior"):
        raise HTTPException(status_code=404, detail="No questions found for this competency")

    return data
