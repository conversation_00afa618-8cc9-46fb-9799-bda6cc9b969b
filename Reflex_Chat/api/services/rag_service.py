import logging
import time
import json
import os
import asyncio
import aiohttp
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple
from sqlmodel import Session
from openai import OpenAI

from Reflex_Chat.api.services.score_calculator_service import UserScoreService

logger = logging.getLogger("service.rag")

class QueryType(str, Enum):
    """Defines the types of queries that can be processed by the RAG system."""
    COMPETENCY_QUESTION = "COMPETENCY_QUESTION"  # Questions about specific competencies, skills, or behaviors
    PERFORMANCE_QUESTION = "PERFORMANCE_QUESTION"  # Questions about the user's own performance or scores
    CLARIFICATION = "CLARIFICATION"  # Questions asking for clarification about a previous answer
    FOLLOW_UP = "FOLLOW_UP"  # Follow-up questions that build on the previous exchange
    META = "META"  # Questions about the system itself or how it works

class RAGService:
    def __init__(self, db: Session):
        self.db = db
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.search_service_name = os.getenv("SEARCH_SERVICE_NAME")
        self.search_api_key = os.getenv("SEARCH_API_KEY")
        self.index_name = os.getenv("INDEX_NAME")

        # Configure thresholds for reranking
        self.confidence_threshold = 0.7
        self.score_gap_threshold = 0.2
        self.max_results = 5

    async def classify_query(self, query: str, conversation_history: Optional[List[Dict[str, str]]] = None) -> QueryType:
        """Classify the query to determine the best processing approach.

        Args:
            query: The user's question
            conversation_history: Previous conversation exchanges

        Returns:
            QueryType: The classified query type
        """
        try:
            # Get the last exchange if available for context
            last_exchange = None
            if conversation_history and len(conversation_history) > 0:
                last_exchange = conversation_history[-1]

            # Create classification prompt
            prompt = f"""Clasifica esta consulta del usuario en una de estas categorías:
            1. COMPETENCY_QUESTION: Preguntas sobre competencias específicas, habilidades o comportamientos
            2. PERFORMANCE_QUESTION: Preguntas sobre el desempeño o puntuaciones de evaluaciones del usuario
            3. CLARIFICATION: Preguntas solicitando aclaración sobre una respuesta anterior
            4. FOLLOW_UP: Preguntas de seguimiento que profundizan en el intercambio anterior
            5. META: Preguntas sobre el sistema mismo o cómo funciona

            Consulta del usuario: "{query}"

            Intercambio anterior (si existe): {last_exchange['question'] + ' / ' + last_exchange['answer'] if last_exchange else 'None'}

            Devuelve solo el nombre de la categoría como una cadena, exactamente como una de las opciones anteriores.
            """

            logger.info(f"Classifying query: {query}")
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "You are a query classifier that returns only the category name."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0,
                max_tokens=20
            )

            category = response.choices[0].message.content.strip()
            logger.info(f"Query classified as: {category}")

            # Convert string to enum value
            try:
                return QueryType(category)
            except ValueError:
                logger.warning(f"Invalid category returned: {category}, defaulting to COMPETENCY_QUESTION")
                return QueryType.COMPETENCY_QUESTION

        except Exception as e:
            logger.error(f"Error classifying query: {e}")
            # Default to competency question if classification fails
            return QueryType.COMPETENCY_QUESTION

    def get_embedding(self, text: str) -> List[float]:
        """Gets OpenAI embedding for a given query text."""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            return list(map(float, response.data[0].embedding))
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None

    def get_conversation_summary(self, history_context: str) -> str:
        """Get a concise summary of conversation history."""
        try:
            summary_prompt = f"Resume la conversación en una oración concisa:\n{history_context}"
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "Resume conversaciones de manera concisa."},
                    {"role": "user", "content": summary_prompt}
                ],
                temperature=0.2,
                max_tokens=100
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return ""

    def get_user_scores(self, user_id: int) -> Dict[str, Any]:
        """Get user evaluation scores summary."""
        try:
            if not user_id:
                logger.warning("No user_id provided for score retrieval")
                return {"error": "No user ID provided"}

            score_service = UserScoreService(self.db)
            scores = score_service.get_user_score_data_summary(user_id)

            # Extract the most relevant information for the assistant
            simplified_scores = {
                "overall_score": scores["overall_score"]["final_score"],
                "top_competencies": [],
                "improvement_areas": []
            }

            # Get top 3 competencies and bottom 3 competencies
            competency_scores = scores["competency_scores"]
            sorted_competencies = sorted(
                [(comp_id, data) for comp_id, data in competency_scores.items()],
                key=lambda x: x[1]["weighted_score"],
                reverse=True
            )

            # Add top competencies
            for _, data in sorted_competencies[:3]:
                simplified_scores["top_competencies"].append({
                    "name": data["name"],
                    "score": data["weighted_score"],
                    "category": data["category"]
                })

            # Add improvement areas (lowest scores)
            for _, data in sorted_competencies[-3:]:
                simplified_scores["improvement_areas"].append({
                    "name": data["name"],
                    "score": data["weighted_score"],
                    "category": data["category"]
                })

            return simplified_scores

        except Exception as e:
            logger.error(f"Error getting user scores: {e}")
            return {"error": f"Error retrieving scores: {str(e)}"}

    async def search_competencies_function(self, query: str, query_type_str: str = "COMPETENCY_QUESTION") -> Dict[str, Any]:
        """Function callable by the LLM to search for competencies with a specific query type.

        Args:
            query: The search query
            query_type_str: The type of query (COMPETENCY_QUESTION, PERFORMANCE_QUESTION, etc.)

        Returns:
            Dict containing search results or error information
        """
        try:
            # Convert string query type to enum
            try:
                query_type = QueryType(query_type_str)
            except ValueError:
                logger.warning(f"Invalid query type: {query_type_str}, defaulting to COMPETENCY_QUESTION")
                query_type = QueryType.COMPETENCY_QUESTION

            logger.info(f"Function call: search_competencies_function with query: '{query}', type: {query_type}")

            # Perform the search with the specified query type
            results = await self.search_competencies(query, query_type)

            if not results or not results.get("value", []):
                return {
                    "success": False,
                    "message": "No competencies found for the query.",
                    "results": []
                }

            # Format results for the function response
            formatted_results = []
            for result in results.get("value", [])[:5]:  # Limit to top 5 results
                formatted_result = {
                    "competency": result.get("name", "Unknown"),
                    "definition": result.get("description", "No definition available"),
                    "context": result.get("categoria", ""),
                    "expert_behaviors": result.get("expert_behavior", []),
                    "talented_behaviors": result.get("talented_behavior", []),
                    "low_skill_behaviors": result.get("low_skill_behavior", []),
                    "development_tips": result.get("development_tips", [])
                }
                formatted_results.append(formatted_result)

            return {
                "success": True,
                "message": f"Found {len(formatted_results)} competencies related to the query.",
                "results": formatted_results
            }

        except Exception as e:
            logger.error(f"Error in search_competencies_function: {e}")
            return {
                "success": False,
                "message": f"Error searching for competencies: {str(e)}",
                "results": []
            }

    def determine_function_needs(self, query_type: QueryType, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Determine which tools should be available based on query type and context."""
        tools = []

        # User scores function
        get_user_scores_function = {
            "type": "function",
            "function": {
                "name": "get_user_scores",
                "description": "Get evaluation scores for a user to provide personalized advice",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "The ID of the user to get scores for"
                        }
                    },
                    "required": ["user_id"],
                    "additionalProperties": False
                },
                "strict": True
            }
        }

        # Search competencies function
        search_competencies_function = {
            "type": "function",
            "function": {
                "name": "search_competencies",
                "description": "Search for competencies related to a specific query",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query for finding relevant competencies"
                        },
                        "query_type_str": {
                            "type": "string",
                            "description": "The type of query (COMPETENCY_QUESTION, PERFORMANCE_QUESTION)",
                            "enum": ["COMPETENCY_QUESTION", "PERFORMANCE_QUESTION"]
                        }
                    },
                    "required": ["query", "query_type_str"],
                    "additionalProperties": False
                },
                "strict": True
            }
        }

        # Add tools based on query type
        if query_type == QueryType.PERFORMANCE_QUESTION and user_id is not None:
            tools.append(get_user_scores_function)
            tools.append(search_competencies_function)  # Also allow searching for performance-related competencies

        elif query_type == QueryType.COMPETENCY_QUESTION:
            tools.append(search_competencies_function)

            # Also add user scores function if we have user context
            if user_id is not None:
                tools.append(get_user_scores_function)

        elif query_type in [QueryType.FOLLOW_UP, QueryType.CLARIFICATION]:
            # For follow-up and clarification, we might need both functions
            tools.append(search_competencies_function)
            if user_id is not None:
                tools.append(get_user_scores_function)

        return tools

    async def get_relevant_messages(self, history: List[Dict[str, str]], query: str) -> str:
        """Retrieve the most relevant messages from history based on semantic similarity to the query."""
        try:
            if not history:
                return ""

            # Get embeddings for the query and each history item
            query_embedding = self.get_embedding(query)
            if not query_embedding:
                return "\n\n".join([f"Usuario: {qa['question']}\nAsistente: {qa['answer']}" for qa in history[-3:]])

            # Format history items and get their embeddings
            history_texts = []
            for qa in history:
                history_text = f"Usuario: {qa['question']}\nAsistente: {qa['answer']}"
                history_texts.append(history_text)

            history_embeddings = []
            for text in history_texts:
                embedding = self.get_embedding(text)
                if embedding:
                    history_embeddings.append(embedding)
                else:
                    # If we can't get embedding, use a placeholder (zeros)
                    history_embeddings.append([0] * len(query_embedding))

            # Calculate cosine similarity between query and each history item
            similarities = []
            for emb in history_embeddings:
                # Calculate dot product
                dot_product = sum(a * b for a, b in zip(query_embedding, emb))
                # Calculate magnitudes
                mag_a = sum(a * a for a in query_embedding) ** 0.5
                mag_b = sum(b * b for b in emb) ** 0.5
                # Calculate cosine similarity
                similarity = dot_product / (mag_a * mag_b) if mag_a * mag_b > 0 else 0
                similarities.append(similarity)

            # Get the top 3 most similar history items
            top_indices = sorted(range(len(similarities)), key=lambda i: similarities[i], reverse=True)[:3]

            # Return the relevant history context
            relevant_history = "\n\n".join([history_texts[i] for i in top_indices])
            return relevant_history

        except Exception as e:
            logger.error(f"Error getting relevant messages: {e}")
            # Fallback to the most recent messages if there's an error
            return "\n\n".join([f"Usuario: {qa['question']}\nAsistente: {qa['answer']}" for qa in history[-3:]])

    def rerank_results(self, query: str, results: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool]:
        """Reranks search results using OpenAI when needed.

        Returns:
            Tuple of (reranked_results, rerank_time, was_reranked)
        """
        start_time = time.time()

        # Check if reranking is needed
        if not results or not results.get("value", []):
            return results, 0, False

        top_results = results.get("value", [])

        # If we have fewer than 2 results, no need to rerank
        if len(top_results) < 2:
            return results, 0, False

        # Check if top score is high enough to skip reranking
        if '@search.score' in top_results[0]:
            top_score = top_results[0]['@search.score']
            second_score = top_results[1]['@search.score'] if len(top_results) > 1 else 0

            # Skip reranking if top score is high or gap is significant
            if top_score > self.confidence_threshold or (top_score - second_score) > self.score_gap_threshold:
                logger.info(f"Skipping reranking - high confidence score: {top_score:.2f}")
                return results, 0, False

        logger.info("Reranking results...")

        # Format results for the reranking prompt
        formatted_results = []
        for idx, result in enumerate(top_results):
            formatted_results.append(f"{idx+1}. {result.get('competency', 'Unknown')}: {result.get('definition', 'No definition')}")

        results_text = "\n".join(formatted_results)

        # Create reranking prompt
        prompt = f"""You are an expert at ranking competencies based on relevance to a query.
Query: {query}

Here are competencies that might be relevant:
{results_text}

Return a JSON array with the indices of the most relevant competencies, ordered from most to least relevant.
Only include the indices, nothing else. Format: [3, 1, 5, 2, 4]
"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "system", "content": "You are a helpful assistant that returns only JSON."},
                          {"role": "user", "content": prompt}],
                temperature=0,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            rerank_data = json.loads(content)

            # Handle different possible response formats (array or object with ranking field)
            if isinstance(rerank_data, list):
                reranked_indices = rerank_data
            else:
                reranked_indices = rerank_data.get("ranking", [])

            # Reorder results based on indices
            original_results = results.get("value", [])
            reranked_results = []
            for idx in reranked_indices:
                idx_adjusted = idx - 1  # Convert 1-based to 0-based index
                if 0 <= idx_adjusted < len(original_results):
                    reranked_results.append(original_results[idx_adjusted])

            # Add any remaining results
            seen_indices = set(idx-1 for idx in reranked_indices if 0 <= idx-1 < len(original_results))
            for i, result in enumerate(original_results):
                if i not in seen_indices:
                    reranked_results.append(result)

            results["value"] = reranked_results[:self.max_results]
            elapsed_time = round(time.time() - start_time, 4)
            logger.info(f"Reranking completed in {elapsed_time:.4f}s")
            return results, elapsed_time, True

        except Exception as e:
            logger.error(f"Error during reranking: {e}")
            elapsed_time = round(time.time() - start_time, 4)
            return results, elapsed_time, False

    async def get_search_strategy(self, query_type: QueryType) -> Dict[str, Any]:
        """Determine the optimal search strategy based on query type."""
        if query_type == QueryType.COMPETENCY_QUESTION:
            # For competency questions, focus on definition and context
            return {
                "perform_search": True,
                "vector_queries": [
                    {
                        "kind": "vector",
                        "fields": "definition_context_embedding",
                        "weight": 1.0,
                        "k": 3
                    },
                    {
                        "kind": "vector",
                        "fields": "category_embedding",
                        "weight": 0.8,
                        "k": 3
                    }
                ],
                "keyword_weight": 0.5,
                "rerank": True
            }
        elif query_type == QueryType.PERFORMANCE_QUESTION:
            # For performance questions, focus on behaviors and development tips
            return {
                "perform_search": True,
                "vector_queries": [
                    {
                        "kind": "vector",
                        "fields": "well_done_embedding",
                        "weight": 0.9,
                        "k": 3
                    },
                    {
                        "kind": "vector",
                        "fields": "badly_done_embedding",
                        "weight": 0.8,
                        "k": 3
                    },
                    {
                        "kind": "vector",
                        "fields": "development_embedding",
                        "weight": 1.0,
                        "k": 3
                    }
                ],
                "keyword_weight": 0.7,
                "rerank": True,
                "prioritize_scores": True
            }
        elif query_type in [QueryType.CLARIFICATION, QueryType.FOLLOW_UP]:
            # For clarification and follow-up, we might not need to search
            return {
                "perform_search": False,
                "use_previous_results": True
            }
        else:  # META or default
            return {
                "perform_search": False,
                "use_system_info": True
            }

    async def search_competencies(self, query: str, query_type: QueryType = QueryType.COMPETENCY_QUESTION) -> Dict[str, Any]:
        """Performs hybrid search with adaptive configuration based on query type and conditional reranking."""
        logger.info(f"Searching for competencies for query: {query} (query type: {query_type})")
        search_start = time.time()

        # Get the appropriate search strategy for this query type
        strategy = await self.get_search_strategy(query_type)

        # Check if we should perform a search at all
        if not strategy.get("perform_search", True):
            logger.info(f"Skipping search based on query type: {query_type}")
            return None

        # Get query embedding
        embedding_start = time.time()
        logger.info("Generating embedding for search query")
        embedding_vector = self.get_embedding(query)
        embedding_time = round(time.time() - embedding_start, 4)
        logger.info(f"Embedding generation completed in {embedding_time:.4f}s")

        if embedding_vector is None:
            logger.warning("No embedding generated, skipping search.")
            return None

        search_url = f"https://{self.search_service_name}.search.windows.net/indexes/{self.index_name}/docs/search?api-version=2024-07-01"

        # Use the vector queries from the strategy
        vector_queries = []
        for vq in strategy.get("vector_queries", []):
            vector_queries.append({
                "kind": "vector",
                "vector": embedding_vector,
                "k": vq.get("k", 3),
                "fields": vq.get("fields"),
                "weight": vq.get("weight", 1.0)
            })

        # Build search payload with adaptive configuration
        search_payload = {
            "search": query,  # Keyword search
            "vectorQueries": vector_queries,
            "select": "code, name, description, categoria, factor, group, role, expert_behavior, low_skill_behavior, talented_behavior, overuse_risks, causes_of_weakness, development_tips, work_assignments, reflection_questions",
            "count": True
        }

        # Add keyword boost if specified
        if "keyword_weight" in strategy:
            search_payload["searchFields"] = "name,description,expert_behavior,talented_behavior,low_skill_behavior"
            search_payload["queryType"] = "full"

        logger.info(f"Searching for: '{query}'")
        logger.info(f"Search strategy: {json.dumps(strategy, indent=2)[:500]}...")
        logger.info(f"Search payload configuration: {json.dumps(search_payload, indent=2)[:500]}...")

        try:
            # Execute search
            search_start = time.time()
            logger.info(f"Calling Azure Search API at {search_url}")

            # Make the HTTP request using aiohttp
            api_call_start = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    search_url,
                    data=json.dumps(search_payload),
                    headers={
                        "Content-Type": "application/json",
                        "api-key": self.search_api_key
                    }
                ) as response:
                    status_code = response.status
                    if status_code != 200:
                        error_text = await response.text()
                        logger.error(f"Error Status Code: {status_code}")
                        logger.error(f"Error Response Content: {error_text}")
                        return None

                    results = await response.json()

            api_call_time = round(time.time() - api_call_start, 4)
            logger.info(f"Azure Search API call completed in {api_call_time:.4f}s")
            logger.info(f"Found {len(results.get('value', []))} results")

            search_time = round(time.time() - search_start, 4)
            logger.info(f"Vector search completed in {search_time:.4f}s")

            # Apply conditional reranking if specified in strategy
            reranked_results = results
            rerank_time = 0
            if strategy.get("rerank", False):
                logger.info("Starting reranking process")
                reranked_results, rerank_time, was_reranked = self.rerank_results(query, results)

                if was_reranked:
                    logger.info(f"Reranking completed in {rerank_time:.4f}s")
                    logger.info(f"Reranking changed the order of results: {was_reranked}")
            else:
                logger.info("Skipping reranking based on strategy")

            total_time = search_time + rerank_time
            logger.info(f"Total search time: {total_time:.4f}s")

            return reranked_results

        except Exception as e:
            logger.error(f"Error performing search: {e}")
            return None

    def generate_adaptive_prompt(self, query_type: QueryType, query: str, search_results: Dict[str, Any],
                                conversation_history: Optional[List[Dict[str, str]]] = None,
                                user_name: Optional[str] = None, user_role: Optional[str] = None) -> str:
        """Generate an adaptive prompt based on query type and context."""
        # Format competency results for the prompt
        formatted_results = []
        for idx, result in enumerate(search_results.get("value", [])[:self.max_results], 1):
            competency = result.get("name", "Unknown")
            definition = result.get("description", "No definition available")
            context = result.get("categoria", "")

            # Convert arrays to bulleted lists
            expert_behaviors = "\n• ".join([""] + result.get("expert_behavior", []))
            low_skill_behaviors = "\n• ".join([""] + result.get("low_skill_behavior", []))
            talented_behaviors = "\n• ".join([""] + result.get("talented_behavior", []))
            overuse_risks = "\n• ".join([""] + result.get("overuse_risks", []))
            causes_weakness = "\n• ".join([""] + result.get("causes_of_weakness", []))
            development_tips = "\n• ".join([""] + result.get("development_tips", []))
            reflection_questions = "\n• ".join([""] + result.get("reflection_questions", []))
            work_assignments = "\n• ".join([""] + result.get("work_assignments", []))

            formatted_result = f"#{idx} - {competency}\n"
            formatted_result += f"Definición: {definition}\n"
            formatted_result += f"Categoría: {context}\n"

            # Add different sections based on query type
            if query_type == QueryType.COMPETENCY_QUESTION:
                # For competency questions, focus on definition, context, and expert behaviors
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
                formatted_result += f"Puntos de reflexión:{reflection_questions}\n"
            elif query_type == QueryType.PERFORMANCE_QUESTION:
                # For performance questions, focus on behaviors and development
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Comportamientos de baja habilidad:{low_skill_behaviors}\n"
                formatted_result += f"Causas de debilidad:{causes_weakness}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
            else:
                # For other types, include all information
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos de baja habilidad:{low_skill_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Riesgos de sobreuso:{overuse_risks}\n"
                formatted_result += f"Causas de debilidad:{causes_weakness}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
                formatted_result += f"Puntos de reflexión:{reflection_questions}\n"
                formatted_result += f"Asignaciones de trabajo:{work_assignments}"

            formatted_results.append(formatted_result)

        results_text = "\n\n".join(formatted_results)

        # Get conversation history to provide context
        history_summary = ""
        relevant_history = ""

        if conversation_history and len(conversation_history) > 0:
            # Create standard history context for reference
            full_history_context = ""
            for qa in conversation_history[-5:]:  # Last 5 exchanges for full context
                full_history_context += f"Usuario: {qa['question']}\n"
                full_history_context += f"Asistente: {qa['answer']}\n\n"

            # Step 1: Summarize long history if needed
            if len(conversation_history) > 5:
                history_summary = self.get_conversation_summary(full_history_context)
                logger.info("Generated history summary")

            # Step 2: Get relevant messages using embeddings
            relevant_history = self.get_relevant_messages(conversation_history, query)
            logger.info("Retrieved relevant history messages")

        relevant_h_section = f"Mensajes relevantes de la conversación:\n{relevant_history}\n\n" if relevant_history else ""
        history_summary_section = f"Resumen de la conversación anterior:\n{history_summary}\n\n" if history_summary else ""

        # Add user information if available
        user_info_section = ""
        if user_name or user_role:
            user_info_section = "Información del usuario:\n"
            if user_name:
                user_info_section += f"Nombre: {user_name}\n"
            if user_role:
                user_info_section += f"Rol: {user_role}\n"
            user_info_section += "\n"

        # Generate query-specific instructions
        instructions = ""
        if query_type == QueryType.COMPETENCY_QUESTION:
            instructions = """
            Analiza la competencia solicitada y proporciona una explicación clara y estructurada.
            Enfócate en qué es la competencia, por qué es importante, y cómo se manifiesta en comportamientos concretos.
            Incluye ejemplos prácticos de cómo se aplica esta competencia en situaciones laborales reales.
            Si es relevante, menciona cómo se puede desarrollar esta competencia con acciones específicas.
            """
        elif query_type == QueryType.PERFORMANCE_QUESTION:
            instructions = """
            Analiza la información proporcionada en relación con el desempeño y evaluación del usuario.
            Enfócate en proporcionar una retroalimentación constructiva y personalizada.
            Destaca tanto fortalezas como áreas de mejora, ofreciendo consejos prácticos y accionables.
            Puedes usar la función get_user_scores para obtener información sobre las evaluaciones del usuario.
            """
        elif query_type == QueryType.FOLLOW_UP:
            instructions = """
            Construye sobre la información previamente compartida, profundizando en los aspectos relevantes.
            Mantén la coherencia con las respuestas anteriores mientras añades nuevo valor.
            Sé específico y concreto en tus explicaciones adicionales.
            """
        elif query_type == QueryType.CLARIFICATION:
            instructions = """
            Proporciona una aclaración directa y concisa sobre la información previamente compartida.
            Asegúrate de abordar específicamente los puntos de confusión.
            Reformula conceptos complejos de manera más accesible si es necesario.
            """
        else:  # META or default
            instructions = """
            Proporciona información clara y precisa sobre el sistema o proceso en cuestión.
            Mantén un tono informativo y útil, explicando las capacidades y limitaciones del sistema.
            """

        # Build the adaptive prompt with all components
        prompt = f"""
        Eres un coach de desarrollo profesional especializado en consultoría de management.

        {user_info_section}{relevant_h_section}{history_summary_section}

        El usuario ha preguntado: "{query}"

        {instructions}

        """

        # Add search results if available
        if results_text:
            prompt += f"""El siguiente bloque de información contiene competencias clave relevantes para esta consulta:

{results_text}

"""

        # Add closing instructions
        prompt += """
        Responde en tono conversacional, directo y útil, enfocándote en lo que será más valioso para el usuario.
        Mantén un tono profesional y cercano, usando el nombre del usuario de manera natural cuando sea apropiado.
        """

        return prompt

    async def process_with_openai(self, query: str, search_results: Dict[str, Any],
                           conversation_history: Optional[List[Dict[str, str]]] = None,
                           user_name: Optional[str] = None,
                           user_role: Optional[str] = None) -> str:
        """Process the search results with OpenAI to generate a response."""
        if not search_results or not search_results.get("value"):
            return "I couldn't find any relevant competencies for your query."

        # Determine the query type to generate an appropriate prompt
        try:
            # Try to classify the query first
            query_type = await self.classify_query(query, conversation_history)
            logger.info(f"Query classified for prompt generation as: {query_type}")
        except Exception as e:
            # Default to COMPETENCY_QUESTION if classification fails
            logger.warning(f"Error classifying query for prompt generation: {e}. Using default type.")
            query_type = QueryType.COMPETENCY_QUESTION

        # Generate an adaptive prompt based on the query type
        prompt = self.generate_adaptive_prompt(
            query_type=query_type,
            query=query,
            search_results=search_results,
            conversation_history=conversation_history,
            user_name=user_name,
            user_role=user_role
        )

        logger.info(f"Generated adaptive prompt for query type: {query_type}")
        return prompt

    async def process_question(self, question: str, conversation_history: Optional[List[Dict[str, str]]] = None,
                      user_name: Optional[str] = None, user_role: Optional[str] = None, user_id: Optional[int] = None) -> str:
        """Process a question using RAG with Azure search and OpenAI."""
        try:
            # 1. Classify the query to determine the best approach
            query_type = await self.classify_query(question, conversation_history)
            logger.info(f"Query classified as: {query_type}")

            # 2. Prepare initial prompt based on query type
            logger.info("Preparing initial prompt")

            # Create an adaptive prompt for the initial call
            prompt = self.generate_adaptive_prompt(
                query_type=query_type,
                query=question,
                search_results={"value": []},  # Empty search results initially
                conversation_history=conversation_history,
                user_name=user_name,
                user_role=user_role
            )

            # 3. Call OpenAI with function definitions
            logger.info("Calling OpenAI with function definitions")

            # Set the appropriate system message based on query type
            if query_type == QueryType.CLARIFICATION:
                system_message = "Eres un coach de desarrollo profesional que aclara dudas sobre respuestas previas."
            elif query_type == QueryType.FOLLOW_UP:
                system_message = "Eres un coach de desarrollo profesional que profundiza en temas previamente discutidos."
            elif query_type == QueryType.META:
                system_message = "Eres un asistente que explica cómo funciona el sistema de coaching y evaluación."
            elif query_type == QueryType.PERFORMANCE_QUESTION:
                system_message = "Eres un coach de desarrollo profesional especializado en analizar el desempeño y proporcionar retroalimentación personalizada."
            else:  # Default for COMPETENCY_QUESTION
                system_message = "Eres un coach de desarrollo profesional para consultoria de negocios. No uses plantillas ni firmas genéricas al final de tus respuestas."

            # Prepare the API call parameters
            api_params = {
                "model": os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }

            # Add function calling based on query type
            tools = self.determine_function_needs(query_type, user_id)
            if tools:
                logger.info(f"Adding {len(tools)} tool definitions for query type: {query_type}")
                api_params["tools"] = tools
                api_params["tool_choice"] = "auto"
                for tool in tools:
                    logger.info(f"Tool definition: {tool['function']['name']}")
            else:
                logger.info("No tool definitions added")

            logger.info(f"Calling OpenAI with model: {api_params['model']}")
            openai_start = time.time()
            response = self.openai_client.chat.completions.create(**api_params)
            openai_time = round(time.time() - openai_start, 4)
            logger.info(f"Initial OpenAI call completed in {openai_time:.4f}s")

            # Check if the model wants to call a tool
            if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
                # Extract tool call details
                tool_calls = response.choices[0].message.tool_calls
                logger.info(f"Tool calls requested: {len(tool_calls)}")

                # Add the assistant's message with tool calls to the conversation
                api_params["messages"].append(response.choices[0].message)

                # Process each tool call
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    logger.info(f"Tool call: {function_name}")
                    logger.info(f"Tool arguments: {tool_call.function.arguments}")

                    # Execute the function
                    if function_name == "get_user_scores":
                        # Parse arguments
                        user_id_arg = function_args.get("user_id", user_id)
                        logger.info(f"Fetching scores for user_id: {user_id_arg}")

                        function_start = time.time()
                        user_scores = self.get_user_scores(user_id_arg)
                        function_time = round(time.time() - function_start, 4)
                        logger.info(f"get_user_scores function completed in {function_time:.4f}s")
                        logger.info(f"User scores summary: {json.dumps(user_scores)[:200]}...")

                        # Add the function result to the conversation
                        api_params["messages"].append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": json.dumps(user_scores)
                        })

                    elif function_name == "search_competencies":
                        # Parse arguments
                        search_query = function_args.get("query", question)
                        query_type_str = function_args.get("query_type_str", "COMPETENCY_QUESTION")

                        logger.info(f"Tool call: search_competencies with query: '{search_query}', type: {query_type_str}")

                        function_start = time.time()
                        search_results = await self.search_competencies_function(search_query, query_type_str)
                        function_time = round(time.time() - function_start, 4)
                        logger.info(f"search_competencies function completed in {function_time:.4f}s")
                        logger.info(f"Search results summary: {json.dumps(search_results)[:200]}...")

                        # Add the function result to the conversation
                        api_params["messages"].append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": json.dumps(search_results)
                        })

                # Call the API again with all the tool results
                logger.info("Calling OpenAI with tool results")
                second_call_start = time.time()
                response = self.openai_client.chat.completions.create(**api_params)
                second_call_time = round(time.time() - second_call_start, 4)
                logger.info(f"Second OpenAI call completed in {second_call_time:.4f}s")

            answer = response.choices[0].message.content
            logger.info("Generated response successfully")
            return answer

        except Exception as e:
            logger.error(f"Error in RAG process: {str(e)}", exc_info=True)
            return f"Lo siento, encontré un error al procesar tu consulta: {str(e)}"

    async def process_question_stream(self, question: str, conversation_history: Optional[List[Dict[str, str]]] = None,
                           user_name: Optional[str] = None, user_role: Optional[str] = None, user_id: Optional[int] = None):
        """Process a question using RAG with Azure search and OpenAI with streaming response."""
        try:
            # 1. Classify the query to determine the best approach
            query_type = await self.classify_query(question, conversation_history)
            logger.info(f"Query classified as: {query_type} (streaming mode)")

            # 2. Search for competencies (if needed based on query type)
            search_results = None

            if query_type in [QueryType.COMPETENCY_QUESTION, QueryType.PERFORMANCE_QUESTION]:
                logger.info(f"Searching for competencies for query: {question}")
                search_results = await self.search_competencies(question, query_type)

                if not search_results or not search_results.get("value", []):
                    logger.warning("No search results found")
                    if query_type == QueryType.COMPETENCY_QUESTION:
                        yield "data: No encontré competencias relevantes para tu consulta. ¿Puedes reformular tu pregunta?\n\n"
                        return

            # 3. Prepare prompt based on query type and search results
            logger.info("Preparing prompt for streaming response")

            # For clarification and follow-up questions, we might not need to search again
            if query_type in [QueryType.CLARIFICATION, QueryType.FOLLOW_UP] and not search_results:
                # For these types, if we don't have search results, we can still proceed with the conversation context
                logger.info(f"Processing {query_type} without new search results (streaming)")

                # Create an adaptive prompt for clarification/follow-up
                prompt = self.generate_adaptive_prompt(
                    query_type=query_type,
                    query=question,
                    search_results={"value": []},  # Empty search results
                    conversation_history=conversation_history,
                    user_name=user_name,
                    user_role=user_role
                )
            elif query_type == QueryType.META:
                # For meta questions about the system itself
                prompt = self.generate_adaptive_prompt(
                    query_type=query_type,
                    query=question,
                    search_results={"value": []},  # Empty search results
                    conversation_history=conversation_history,
                    user_name=user_name,
                    user_role=user_role
                )
            else:
                # For competency and performance questions, use the standard process
                prompt = await self.process_with_openai(
                    query=question,
                    search_results=search_results,
                    conversation_history=conversation_history,
                    user_name=user_name,
                    user_role=user_role
                )

            # 4. Call OpenAI with streaming to get answer
            logger.info("Calling OpenAI with streaming")
            #logger.info(f"Prompt: {prompt}")

            # Set the appropriate system message based on query type
            if query_type == QueryType.CLARIFICATION:
                system_message = "Eres un coach de desarrollo profesional que aclara dudas sobre respuestas previas."
            elif query_type == QueryType.FOLLOW_UP:
                system_message = "Eres un coach de desarrollo profesional que profundiza en temas previamente discutidos."
            elif query_type == QueryType.META:
                system_message = "Eres un asistente que explica cómo funciona el sistema de coaching y evaluación."
            elif query_type == QueryType.PERFORMANCE_QUESTION:
                system_message = "Eres un coach de desarrollo profesional especializado en analizar el desempeño y proporcionar retroalimentación personalizada."
            else:  # Default for COMPETENCY_QUESTION
                system_message = "Eres un coach de desarrollo profesional para consultoria de negocios. No uses plantillas ni firmas genéricas al final de tus respuestas."

            # Prepare the API call parameters
            api_params = {
                "model": os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 1000,
                "stream": True
            }

            # Add function calling based on query type
            tools = self.determine_function_needs(query_type, user_id)
            if tools:
                logger.info(f"Adding {len(tools)} tool definitions for streaming query type: {query_type}")
                api_params["tools"] = tools
                api_params["tool_choice"] = "auto"
                for tool in tools:
                    logger.info(f"Tool definition: {tool['function']['name']}")

            # For streaming, we need to handle tool calls differently
            # First, make a non-streaming call to check if a tool is needed
            if tools:
                logger.info("Streaming with tool calling: making initial non-streaming call")
                non_stream_params = api_params.copy()
                non_stream_params["stream"] = False

                initial_call_start = time.time()
                initial_response = self.openai_client.chat.completions.create(**non_stream_params)
                initial_call_time = round(time.time() - initial_call_start, 4)
                logger.info(f"Initial non-streaming call completed in {initial_call_time:.4f}s")

                # Check if the model wants to call a tool
                if hasattr(initial_response.choices[0].message, 'tool_calls') and initial_response.choices[0].message.tool_calls:
                    # Extract tool call details
                    tool_calls = initial_response.choices[0].message.tool_calls
                    logger.info(f"Tool calls requested in streaming mode: {len(tool_calls)}")

                    # Add the assistant's message with tool calls to the conversation
                    api_params["messages"].append(initial_response.choices[0].message)

                    # Process each tool call
                    for tool_call in tool_calls:
                        function_name = tool_call.function.name
                        function_args = json.loads(tool_call.function.arguments)
                        logger.info(f"Tool call in streaming mode: {function_name}")
                        logger.info(f"Tool arguments: {tool_call.function.arguments}")

                        # Execute the function
                        if function_name == "get_user_scores":
                            # Parse arguments
                            user_id_arg = function_args.get("user_id", user_id)
                            logger.info(f"Fetching scores for user_id: {user_id_arg}")

                            function_start = time.time()
                            user_scores = self.get_user_scores(user_id_arg)
                            function_time = round(time.time() - function_start, 4)
                            logger.info(f"get_user_scores function completed in {function_time:.4f}s")
                            logger.info(f"User scores summary for streaming: {json.dumps(user_scores)[:200]}...")

                            # Add the function result to the conversation
                            api_params["messages"].append({
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": json.dumps(user_scores)
                            })

                        elif function_name == "search_competencies":
                            # Parse arguments
                            search_query = function_args.get("query", question)
                            query_type_str = function_args.get("query_type_str", "COMPETENCY_QUESTION")

                            logger.info(f"Tool call: search_competencies with query: '{search_query}', type: {query_type_str}")

                            function_start = time.time()
                            search_results = await self.search_competencies_function(search_query, query_type_str)
                            function_time = round(time.time() - function_start, 4)
                            logger.info(f"search_competencies function completed in {function_time:.4f}s")
                            logger.info(f"Search results summary: {json.dumps(search_results)[:200]}...")

                            # Add the function result to the conversation
                            api_params["messages"].append({
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": json.dumps(search_results)
                            })

                    logger.info("Updated streaming messages with tool results")

            # Now create the streaming session
            logger.info("Creating streaming session with OpenAI")
            streaming_start = time.time()
            session = self.openai_client.chat.completions.create(**api_params)

            # 4. Stream the results
            logger.info("Starting to stream results")
            chunk_count = 0
            total_tokens = 0

            for item in session:
                chunk_count += 1
                if hasattr(item.choices[0].delta, "content"):
                    answer_text = item.choices[0].delta.content
                    if answer_text is not None:
                        total_tokens += len(answer_text.split())
                        if chunk_count % 10 == 0:  # Log every 10 chunks to avoid excessive logging
                            logger.info(f"Streaming chunk #{chunk_count}, tokens so far: {total_tokens}")
                        yield f"data: {answer_text}\n\n"

            streaming_time = round(time.time() - streaming_start, 4)
            logger.info(f"Streaming completed in {streaming_time:.4f}s")
            logger.info(f"Streamed {chunk_count} chunks with approximately {total_tokens} tokens")

            # End of stream marker
            logger.info("Sending end of stream marker")
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"Error in RAG streaming process: {str(e)}", exc_info=True)
            yield f"data: Lo siento, encontré un error al procesar tu consulta: {str(e)}\n\n"
