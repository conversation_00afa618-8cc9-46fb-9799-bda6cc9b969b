import logging
import time
import json
import os
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, TypedDict
from enum import Enum
from openai import OpenAI
from sqlmodel import Session
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_core.tools import Tool
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.memory import BaseMemory
from langchain.memory import ConversationBufferMemory
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain_core.messages import SystemMessage

from Reflex_Chat.api.services.score_calculator_service import UserScoreService

# Configure logging
logger = logging.getLogger("service.langgraph_rag")
logger.setLevel(logging.INFO)

class QueryType(str, Enum):
    """Defines the types of queries that can be processed by the RAG system."""
    COMPETENCY_QUESTION = "COMPETENCY_QUESTION"  # Questions about specific competencies, skills, or behaviors
    PERFORMANCE_QUESTION = "PERFORMANCE_QUESTION"  # Questions about the user's own performance or scores
    CLARIFICATION = "CLARIFICATION"  # Questions asking for clarification about a previous answer
    FOLLOW_UP = "FOLLOW_UP"  # Follow-up questions that build on the previous exchange
    META = "META"  # Questions about the system itself or how it works

class AgentState(TypedDict):
    """Type definition for the agent's state."""
    memory: BaseMemory
    query_type: Optional[QueryType]
    search_results: Optional[Dict[str, Any]]
    user_scores: Optional[Dict[str, Any]]
    current_step: str
    user_id: Optional[int]
    user_name: Optional[str]
    user_role: Optional[str]
    next_step: Optional[str]
    agent_output: Optional[Dict[str, Any]]
    tool_calls: Optional[List[Dict[str, Any]]]

class LangGraphRAGService:
    def __init__(self, db: Session):
        self.db = db
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.search_service_name = os.getenv("SEARCH_SERVICE_NAME")
        self.search_api_key = os.getenv("SEARCH_API_KEY")
        self.index_name = os.getenv("INDEX_NAME")

        # Configure thresholds for reranking
        self.confidence_threshold = 0.7
        self.score_gap_threshold = 0.2
        self.max_results = 5

        # Initialize LangChain components
        self.llm = ChatOpenAI(
            model=os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
            temperature=0.3
        )

        # Create tools
        self.tools = [
            Tool.from_function(
                func=self.search_competencies_tool,
                name="search_competencies",
                description="Search for competencies related to the query. Use this when the user asks about specific skills, behaviors, or competency definitions.",
                return_direct=False
            ),
            Tool.from_function(
                func=self.get_user_scores_tool,
                name="get_user_scores",
                description="Get performance scores for the specified user. Use this when the user asks about their evaluation scores or performance metrics.",
                return_direct=False
            )
        ]

        # Create agent with tools
        self._setup_agent()

        # Create the graph
        self.workflow = self._create_workflow()

    def _setup_agent(self):
        """Set up the LangChain agent with tools."""
        # Create a prompt template for the agent
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""You are an AI assistant that helps with professional development coaching.
You have access to tools that can search for competency information and retrieve user performance scores.

For questions about specific competencies, skills, or behaviors, use the search_competencies tool.
For questions about user performance or evaluation scores, use the get_user_scores tool.

Only use the tools when necessary. If the question can be answered without using tools,
or if it's a clarification or follow-up question, you can respond directly.

Always provide helpful, professional responses focused on professional development."""),
            MessagesPlaceholder(variable_name="chat_history"),
            HumanMessage(content="{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])

        # Create the agent
        self.agent = create_openai_functions_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )

        # Create the agent executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True
        )

    def _create_workflow(self) -> StateGraph:
        """Create the LangGraph workflow with conditional branching and tool calling."""
        # Define the nodes
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("classify_query", self._classify_query)
        workflow.add_node("agent_tool_calling", self._agent_tool_calling)
        workflow.add_node("search_competencies", self._search_competencies)
        workflow.add_node("get_user_scores", self._get_user_scores)
        workflow.add_node("generate_response", self._generate_response)

        # Add conditional edges based on query type
        workflow.add_conditional_edges(
            "classify_query",
            lambda state: state["query_type"].value if state.get("query_type") else QueryType.COMPETENCY_QUESTION.value,
            {
                QueryType.COMPETENCY_QUESTION.value: "agent_tool_calling",
                QueryType.PERFORMANCE_QUESTION.value: "agent_tool_calling",
                QueryType.META.value: "generate_response",
                QueryType.CLARIFICATION.value: "generate_response",
                QueryType.FOLLOW_UP.value: "agent_tool_calling",
                "__default__": "agent_tool_calling"
            }
        )

        # Add conditional edges from agent_tool_calling
        workflow.add_conditional_edges(
            "agent_tool_calling",
            lambda state: state.get("next_step", "generate_response"),
            {
                "search_competencies": "search_competencies",
                "get_user_scores": "get_user_scores",
                "generate_response": "generate_response",
                "__default__": "generate_response"
            }
        )

        # Add remaining edges
        workflow.add_edge("search_competencies", "get_user_scores")
        workflow.add_edge("get_user_scores", "generate_response")
        workflow.add_edge("generate_response", END)

        # Set entry point
        workflow.set_entry_point("classify_query")

        return workflow.compile()

    def get_embedding(self, text: str) -> List[float]:
        """Gets OpenAI embedding for a given query text."""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            return list(map(float, response.data[0].embedding))
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None

    def get_conversation_summary(self, history_context: str) -> str:
        """Get a concise summary of conversation history."""
        try:
            summary_prompt = f"Resume la conversación en una oración concisa:\n{history_context}"
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "Resume conversaciones de manera concisa."},
                    {"role": "user", "content": summary_prompt}
                ],
                temperature=0.2,
                max_tokens=100
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return ""

    async def get_relevant_messages(self, history: List[Dict[str, str]], query: str) -> str:
        """Retrieve the most relevant messages from history based on semantic similarity to the query."""
        try:
            if not history:
                return ""

            # Get embeddings for the query and each history item
            query_embedding = self.get_embedding(query)
            if not query_embedding:
                return "\n\n".join([f"Usuario: {qa['content']}\nAsistente: {qa.get('assistant', '')}" for qa in history[-3:]])

            # Format history items and get their embeddings
            history_texts = []
            for qa in history:
                history_text = f"Usuario: {qa['content']}\nAsistente: {qa.get('assistant', '')}"
                history_texts.append(history_text)

            history_embeddings = []
            for text in history_texts:
                embedding = self.get_embedding(text)
                if embedding:
                    history_embeddings.append(embedding)
                else:
                    # If we can't get embedding, use a placeholder (zeros)
                    history_embeddings.append([0] * len(query_embedding))

            # Calculate cosine similarity between query and each history item
            similarities = []
            for emb in history_embeddings:
                # Calculate dot product
                dot_product = sum(a * b for a, b in zip(query_embedding, emb))
                # Calculate magnitudes
                mag_a = sum(a * a for a in query_embedding) ** 0.5
                mag_b = sum(b * b for b in emb) ** 0.5
                # Calculate cosine similarity
                similarity = dot_product / (mag_a * mag_b) if mag_a * mag_b > 0 else 0
                similarities.append(similarity)

            # Get the top 3 most similar history items
            top_indices = sorted(range(len(similarities)), key=lambda i: similarities[i], reverse=True)[:3]

            # Return the relevant history context
            relevant_history = "\n\n".join([history_texts[i] for i in top_indices])
            return relevant_history

        except Exception as e:
            logger.error(f"Error getting relevant messages: {e}")
            # Fallback to the most recent messages if there's an error
            return "\n\n".join([f"Usuario: {qa['content']}\nAsistente: {qa.get('assistant', '')}" for qa in history[-3:]])

    async def _classify_query(self, state: AgentState) -> AgentState:
        """Classify the query type."""
        try:
            logger.info("Step 1: Classifying query type")

            # Get the last message from memory
            memory = state["memory"]
            messages = memory.chat_memory.messages
            last_message = messages[-1].content if messages else ""

            # Get the last exchange if available for context
            last_exchange = None
            if len(messages) > 1:
                last_exchange = {
                    "question": messages[-2].content,
                    "answer": messages[-1].content
                }

            # Create classification prompt
            prompt = f"""Clasifica esta consulta del usuario en una de estas categorías:
            1. COMPETENCY_QUESTION: Preguntas sobre competencias específicas, habilidades o comportamientos
            2. PERFORMANCE_QUESTION: Preguntas sobre el desempeño o puntuaciones de evaluaciones del usuario
            3. CLARIFICATION: Preguntas solicitando aclaración sobre una respuesta anterior
            4. FOLLOW_UP: Preguntas de seguimiento que profundizan en el intercambio anterior
            5. META: Preguntas sobre el sistema mismo o cómo funciona

            Consulta del usuario: "{last_message}"

            Intercambio anterior (si existe): {last_exchange['question'] + ' / ' + last_exchange['answer'] if last_exchange else 'None'}

            Devuelve solo el nombre de la categoría como una cadena, exactamente como una de las opciones anteriores.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "You are a query classifier that returns only the category name."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0,
                max_tokens=20
            )

            category = response.choices[0].message.content.strip()
            query_type = QueryType(category)

            logger.info(f"Query classified as: {query_type.value}")
            return {**state, "query_type": query_type, "current_step": "search_competencies"}
        except Exception as e:
            logger.error(f"Error in query classification: {e}")
            return {**state, "query_type": QueryType.COMPETENCY_QUESTION, "current_step": "search_competencies"}

    async def search_competencies_tool(self, query: str) -> Dict[str, Any]:
        """Search for competencies related to the query.

        Args:
            query: The search query

        Returns:
            Dict containing search results or error information
        """
        try:
            logger.info(f"Tool call: search_competencies_tool with query: '{query}'")

            # Get query embedding
            embedding_vector = self.get_embedding(query)
            if not embedding_vector:
                logger.warning("No embedding generated, skipping search.")
                return {
                    "success": False,
                    "message": "Failed to generate embedding for search",
                    "results": []
                }

            search_url = f"https://{self.search_service_name}.search.windows.net/indexes/{self.index_name}/docs/search?api-version=2024-07-01"

            # Build search payload
            search_payload = {
                "search": query,
                "vectorQueries": [{
                    "kind": "vector",
                    "vector": embedding_vector,
                    "k": 3,
                    "fields": "definition_context_embedding",
                    "weight": 1.0
                }],
                "select": "code, name, description, categoria, factor, group, role, expert_behavior, low_skill_behavior, talented_behavior, overuse_risks, causes_of_weakness, development_tips, work_assignments, reflection_questions",
                "count": True
            }

            # Execute search
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    search_url,
                    data=json.dumps(search_payload),
                    headers={
                        "Content-Type": "application/json",
                        "api-key": self.search_api_key
                    }
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Error Status Code: {response.status}")
                        logger.error(f"Error Response Content: {error_text}")
                        return {
                            "success": False,
                            "message": f"Search API error: {response.status}",
                            "results": []
                        }

                    results = await response.json()

            # Rerank results if needed
            if results and results.get("value", []):
                results = await self._rerank_results(query, results)

            # Format results for the function response
            formatted_results = []
            for result in results.get("value", [])[:self.max_results]:
                formatted_result = {
                    "competency": result.get("name", "Unknown"),
                    "definition": result.get("description", "No definition available"),
                    "context": result.get("categoria", ""),
                    "expert_behaviors": result.get("expert_behavior", []),
                    "talented_behaviors": result.get("talented_behavior", []),
                    "low_skill_behaviors": result.get("low_skill_behavior", []),
                    "development_tips": result.get("development_tips", [])
                }
                formatted_results.append(formatted_result)

            logger.info(f"Found {len(formatted_results)} relevant competencies")
            return {
                "success": True,
                "message": f"Found {len(formatted_results)} competencies related to the query.",
                "results": formatted_results
            }

        except Exception as e:
            logger.error(f"Error in competency search tool: {e}")
            return {
                "success": False,
                "message": f"Error searching competencies: {str(e)}",
                "results": []
            }

    async def _search_competencies(self, state: AgentState) -> AgentState:
        """Search for relevant competencies."""
        try:
            logger.info("Step 3: Searching competencies")

            # Check if we have tool calls from the agent
            tool_calls = state.get("tool_calls", [])
            search_tool_call = None

            # Look for search_competencies tool call
            try:
                for tool_call in tool_calls:
                    if tool_call.get("tool") == "search_competencies":
                        search_tool_call = tool_call
                        break

                if search_tool_call:
                    # Use the input from the agent's tool call
                    query = search_tool_call.get("input")
                    logger.info(f"Using agent's search query: {query}")

                    # The agent already called the tool, use the output
                    search_results = search_tool_call.get("output")

                    if search_results and search_results.get("success", False):
                        # Convert tool results back to the format expected by the rest of the code
                        formatted_results = {
                            "value": search_results.get("results", [])
                        }
                        logger.info(f"Found {len(search_results.get('results', []))} relevant competencies from agent tool call")
                        return {**state, "search_results": formatted_results, "current_step": "get_user_scores"}
                    else:
                        logger.warning(f"Search from agent tool call failed or returned no results")
            except Exception as tool_error:
                logger.error(f"Error processing agent tool call: {tool_error}")
                # Continue with fallback search

            # Fallback: No valid tool call from agent, use the original query
            try:
                memory = state["memory"]
                messages = memory.chat_memory.messages
                query = messages[-1].content if messages else ""

                if not query:
                    logger.warning("No query found for search, skipping")
                    return {**state, "current_step": "get_user_scores"}

                logger.info(f"Performing fallback search with query: {query}")

                # Use the tool to search competencies
                search_results = await self.search_competencies_tool(query)

                if search_results["success"] and search_results.get("results"):
                    # Convert tool results back to the format expected by the rest of the code
                    formatted_results = {
                        "value": search_results["results"]
                    }
                    logger.info(f"Found {len(search_results['results'])} relevant competencies in fallback search")
                    return {**state, "search_results": formatted_results, "current_step": "get_user_scores"}
                else:
                    logger.warning(f"Fallback search failed: {search_results.get('message')}")
            except Exception as fallback_error:
                logger.error(f"Error in fallback search: {fallback_error}")

            logger.info("Proceeding to next step without search results")
            return {**state, "current_step": "get_user_scores"}
        except Exception as e:
            logger.error(f"Error in competency search: {e}")
            return {**state, "current_step": "get_user_scores"}

    async def _rerank_results(self, query: str, results: Dict[str, Any]) -> Dict[str, Any]:
        """Reranks search results using OpenAI when needed."""
        start_time = time.time()

        if not results or not results.get("value", []):
            return results

        top_results = results.get("value", [])

        if len(top_results) < 2:
            return results

        if '@search.score' in top_results[0]:
            top_score = top_results[0]['@search.score']
            second_score = top_results[1]['@search.score'] if len(top_results) > 1 else 0

            if top_score > self.confidence_threshold or (top_score - second_score) > self.score_gap_threshold:
                logger.info(f"Skipping reranking - high confidence score: {top_score:.2f}")
                return results

        logger.info("Reranking results...")

        formatted_results = []
        for idx, result in enumerate(top_results):
            formatted_results.append(f"{idx+1}. {result.get('competency', 'Unknown')}: {result.get('definition', 'No definition')}")

        results_text = "\n".join(formatted_results)

        prompt = f"""You are an expert at ranking competencies based on relevance to a query.
Query: {query}

Here are competencies that might be relevant:
{results_text}

Return a JSON array with the indices of the most relevant competencies, ordered from most to least relevant.
Only include the indices, nothing else. Format: [3, 1, 5, 2, 4]
"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "system", "content": "You are a helpful assistant that returns only JSON."},
                          {"role": "user", "content": prompt}],
                temperature=0,
                response_format={"type": "json_object"}
            )

            content = response.choices[0].message.content
            rerank_data = json.loads(content)

            if isinstance(rerank_data, list):
                reranked_indices = rerank_data
            else:
                reranked_indices = rerank_data.get("ranking", [])

            original_results = results.get("value", [])
            reranked_results = []
            for idx in reranked_indices:
                idx_adjusted = idx - 1
                if 0 <= idx_adjusted < len(original_results):
                    reranked_results.append(original_results[idx_adjusted])

            seen_indices = set(idx-1 for idx in reranked_indices if 0 <= idx-1 < len(original_results))
            for i, result in enumerate(original_results):
                if i not in seen_indices:
                    reranked_results.append(result)

            results["value"] = reranked_results[:self.max_results]
            elapsed_time = round(time.time() - start_time, 4)
            logger.info(f"Reranking completed in {elapsed_time:.4f}s")
            return results

        except Exception as e:
            logger.error(f"Error during reranking: {e}")
            return results

    async def get_user_scores_tool(self, user_id: int) -> Dict[str, Any]:
        """Get performance scores for the specified user.

        Args:
            user_id: The ID of the user to get scores for

        Returns:
            Dict containing user score data or error information
        """
        try:
            logger.info(f"Tool call: get_user_scores_tool for user_id: {user_id}")

            if not user_id:
                return {
                    "success": False,
                    "message": "No user ID provided",
                    "scores": {}
                }

            score_service = UserScoreService(self.db)
            scores = score_service.get_user_score_data_summary(user_id)

            simplified_scores = {
                "overall_score": scores["overall_score"]["final_score"],
                "top_competencies": [],
                "improvement_areas": []
            }

            competency_scores = scores["competency_scores"]
            sorted_competencies = sorted(
                [(comp_id, data) for comp_id, data in competency_scores.items()],
                key=lambda x: x[1]["weighted_score"],
                reverse=True
            )

            for _, data in sorted_competencies[:3]:
                simplified_scores["top_competencies"].append({
                    "name": data["name"],
                    "score": data["weighted_score"],
                    "category": data["category"]
                })

            for _, data in sorted_competencies[-3:]:
                simplified_scores["improvement_areas"].append({
                    "name": data["name"],
                    "score": data["weighted_score"],
                    "category": data["category"]
                })

            logger.info("User scores retrieved successfully")
            return {
                "success": True,
                "message": "User scores retrieved successfully",
                "scores": simplified_scores
            }

        except Exception as e:
            logger.error(f"Error in user scores tool: {e}")
            return {
                "success": False,
                "message": f"Error retrieving user scores: {str(e)}",
                "scores": {}
            }

    async def _get_user_scores(self, state: AgentState) -> AgentState:
        """Get user scores if needed."""
        try:
            logger.info("Step 4: Retrieving user scores")

            # Check if we have tool calls from the agent
            tool_calls = state.get("tool_calls", [])
            scores_tool_call = None

            # Look for get_user_scores tool call
            try:
                for tool_call in tool_calls:
                    if tool_call.get("tool") == "get_user_scores":
                        scores_tool_call = tool_call
                        break

                if scores_tool_call:
                    # The agent already called the tool, use the output
                    user_scores_result = scores_tool_call.get("output")

                    if user_scores_result and user_scores_result.get("success", False):
                        logger.info("User scores retrieved successfully from agent tool call")
                        return {**state, "user_scores": user_scores_result.get("scores", {}), "current_step": "generate_response"}
                    else:
                        logger.warning(f"User scores retrieval from agent tool call failed or returned no results")
            except Exception as tool_error:
                logger.error(f"Error processing agent tool call for scores: {tool_error}")
                # Continue with fallback

            # Fallback: Check if we need scores based on query type
            try:
                if state["query_type"] == QueryType.PERFORMANCE_QUESTION and state.get("user_id"):
                    logger.info(f"Performing fallback user scores retrieval for user_id: {state.get('user_id')}")

                    # No tool call from agent, but we need scores based on query type
                    user_scores_result = await self.get_user_scores_tool(state["user_id"])

                    if user_scores_result["success"] and user_scores_result.get("scores"):
                        logger.info("User scores retrieved successfully in fallback")
                        return {**state, "user_scores": user_scores_result["scores"], "current_step": "generate_response"}
                    else:
                        logger.warning(f"Fallback user scores retrieval failed: {user_scores_result.get('message')}")
            except Exception as fallback_error:
                logger.error(f"Error in fallback user scores retrieval: {fallback_error}")

            logger.info("Proceeding to response generation without user scores")
            return {**state, "current_step": "generate_response"}
        except Exception as e:
            logger.error(f"Error in user scores retrieval: {e}")
            return {**state, "current_step": "generate_response"}

    async def _create_response_prompt(self, state: AgentState) -> str:
        """Create the appropriate prompt based on query type and available data."""
        query_type = state["query_type"]
        memory = state["memory"]
        messages = memory.chat_memory.messages
        query = messages[-1].content if messages else ""
        search_results = state.get("search_results", {})
        user_scores = state.get("user_scores", {})
        user_name = state.get("user_name")
        user_role = state.get("user_role")

        # Format competency results for the prompt
        formatted_results = []
        for idx, result in enumerate(search_results.get("value", [])[:self.max_results], 1):
            competency = result.get("name", "Unknown")
            definition = result.get("description", "No definition available")
            context = result.get("categoria", "")

            expert_behaviors = "\n• ".join([""] + result.get("expert_behavior", []))
            low_skill_behaviors = "\n• ".join([""] + result.get("low_skill_behavior", []))
            talented_behaviors = "\n• ".join([""] + result.get("talented_behavior", []))
            overuse_risks = "\n• ".join([""] + result.get("overuse_risks", []))
            causes_weakness = "\n• ".join([""] + result.get("causes_of_weakness", []))
            development_tips = "\n• ".join([""] + result.get("development_tips", []))
            reflection_questions = "\n• ".join([""] + result.get("reflection_questions", []))
            work_assignments = "\n• ".join([""] + result.get("work_assignments", []))

            formatted_result = f"#{idx} - {competency}\n"
            formatted_result += f"Definición: {definition}\n"
            formatted_result += f"Categoría: {context}\n"

            if query_type == QueryType.COMPETENCY_QUESTION:
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
                formatted_result += f"Puntos de reflexión:{reflection_questions}\n"
            elif query_type == QueryType.PERFORMANCE_QUESTION:
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Comportamientos de baja habilidad:{low_skill_behaviors}\n"
                formatted_result += f"Causas de debilidad:{causes_weakness}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
            else:
                formatted_result += f"Comportamientos expertos:{expert_behaviors}\n"
                formatted_result += f"Comportamientos de baja habilidad:{low_skill_behaviors}\n"
                formatted_result += f"Comportamientos talentosos:{talented_behaviors}\n"
                formatted_result += f"Riesgos de sobreuso:{overuse_risks}\n"
                formatted_result += f"Causas de debilidad:{causes_weakness}\n"
                formatted_result += f"Consejos de desarrollo:{development_tips}\n"
                formatted_result += f"Puntos de reflexión:{reflection_questions}\n"
                formatted_result += f"Asignaciones de trabajo:{work_assignments}"

            formatted_results.append(formatted_result)

        results_text = "\n\n".join(formatted_results)

        # Format user scores if available
        scores_text = ""
        if user_scores and query_type == QueryType.PERFORMANCE_QUESTION:
            scores_text = "Información de evaluación del usuario:\n"

            if "overall_score" in user_scores:
                scores_text += f"Puntuación general: {user_scores['overall_score']:.1f}%\n\n"

            if "top_competencies" in user_scores and user_scores["top_competencies"]:
                scores_text += "Competencias destacadas:\n"
                for comp in user_scores["top_competencies"]:
                    scores_text += f"• {comp.get('name')}: {comp.get('score'):.1f}% ({comp.get('category')})\n"
                scores_text += "\n"

            if "improvement_areas" in user_scores and user_scores["improvement_areas"]:
                scores_text += "Áreas de mejora:\n"
                for comp in user_scores["improvement_areas"]:
                    scores_text += f"• {comp.get('name')}: {comp.get('score'):.1f}% ({comp.get('category')})\n"
                scores_text += "\n"

        # Get conversation history to provide context
        history_summary = ""
        relevant_history = ""

        if len(messages) > 1:
            full_history_context = ""
            for msg in messages[-5:]:
                if isinstance(msg, HumanMessage):
                    full_history_context += f"Usuario: {msg.content}\n"
                elif isinstance(msg, AIMessage):
                    full_history_context += f"Asistente: {msg.content}\n\n"

            if len(messages) > 5:
                history_summary = self.get_conversation_summary(full_history_context)

            # Convert messages to the format expected by get_relevant_messages
            history_list = []
            for msg in messages:
                if isinstance(msg, HumanMessage):
                    history_list.append({"content": msg.content, "role": "user"})
                elif isinstance(msg, AIMessage):
                    history_list.append({"content": msg.content, "role": "assistant"})

            relevant_history = await self.get_relevant_messages(history_list, query)

        relevant_h_section = f"Mensajes relevantes de la conversación:\n{relevant_history}\n\n" if relevant_history else ""
        history_summary_section = f"Resumen de la conversación anterior:\n{history_summary}\n\n" if history_summary else ""

        user_info_section = ""
        if user_name or user_role:
            user_info_section = "Información del usuario:\n"
            if user_name:
                user_info_section += f"Nombre: {user_name}\n"
            if user_role:
                user_info_section += f"Rol: {user_role}\n"
            user_info_section += "\n"

        instructions = {
            QueryType.COMPETENCY_QUESTION: """
            Analiza la competencia solicitada y proporciona una explicación clara y estructurada.
            Enfócate en qué es la competencia, por qué es importante, y cómo se manifiesta en comportamientos concretos.
            Incluye ejemplos prácticos de cómo se aplica esta competencia en situaciones laborales reales.
            Si es relevante, menciona cómo se puede desarrollar esta competencia con acciones específicas.
            """,
            QueryType.PERFORMANCE_QUESTION: """
            Analiza la información proporcionada en relación con el desempeño y evaluación del usuario.
            Enfócate en proporcionar una retroalimentación constructiva y personalizada.
            Destaca tanto fortalezas como áreas de mejora, ofreciendo consejos prácticos y accionables.
            """,
            QueryType.FOLLOW_UP: """
            Construye sobre la información previamente compartida, profundizando en los aspectos relevantes.
            Mantén la coherencia con las respuestas anteriores mientras añades nuevo valor.
            Sé específico y concreto en tus explicaciones adicionales.
            """,
            QueryType.CLARIFICATION: """
            Proporciona una aclaración directa y concisa sobre la información previamente compartida.
            Asegúrate de abordar específicamente los puntos de confusión.
            Reformula conceptos complejos de manera más accesible si es necesario.
            """,
            QueryType.META: """
            Proporciona información clara y precisa sobre el sistema o proceso en cuestión.
            Mantén un tono informativo y útil, explicando las capacidades y limitaciones del sistema.
            """
        }.get(query_type, "Eres un coach de desarrollo profesional.")

        prompt = f"""
        Eres un coach de desarrollo profesional especializado en consultoría de management.

        {user_info_section}{relevant_h_section}{history_summary_section}

        El usuario ha preguntado: "{query}"

        {instructions}

        """

        if scores_text:
            prompt += f"""Información de evaluación del usuario:

{scores_text}

"""

        if results_text:
            prompt += f"""El siguiente bloque de información contiene competencias clave relevantes para esta consulta:

{results_text}

"""

        prompt += """
        Responde en tono conversacional, directo y útil, enfocándote en lo que será más valioso para el usuario.
        Mantén un tono profesional y cercano, usando el nombre del usuario de manera natural cuando sea apropiado.
        """

        return prompt

    async def _generate_response(self, state: AgentState) -> AgentState:
        """Generate the final response."""
        try:
            logger.info("Step 5: Generating response")

            # Check if we have agent output to use directly
            agent_output = state.get("agent_output")
            memory = state["memory"]

            if agent_output and "output" in agent_output:
                # Use the agent's output directly
                answer = agent_output["output"]
                logger.info("Using agent's direct output for response")
            else:
                # Generate response using our standard prompt
                prompt = await self._create_response_prompt(state)

                response = self.openai_client.chat.completions.create(
                    model=os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
                    messages=[
                        {"role": "system", "content": "Eres un coach de desarrollo profesional para consultoria de negocios."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.3,
                    max_tokens=1000
                )

                answer = response.choices[0].message.content
                logger.info("Response generated using standard prompt")

            # Add the response to memory
            memory.chat_memory.add_ai_message(answer)

            return {**state, "current_step": "end"}
        except Exception as e:
            logger.error(f"Error in response generation: {e}")
            error_message = f"Lo siento, encontré un error al procesar tu consulta: {str(e)}"

            # Add error message to memory
            memory = state["memory"]
            memory.chat_memory.add_ai_message(error_message)

            return {**state, "current_step": "end"}

    async def _agent_tool_calling(self, state: AgentState) -> AgentState:
        """Use the agent to determine which tools to call."""
        try:
            logger.info("Step 2: Agent tool calling")

            # Get the last message from memory
            memory = state["memory"]
            messages = memory.chat_memory.messages
            query = messages[-1].content if messages else ""

            if not query:
                logger.warning("No query found in memory, skipping agent tool calling")
                return {**state, "next_step": "generate_response", "current_step": "generate_response"}

            # Get chat history for the agent
            chat_history = []
            try:
                for msg in messages[:-1]:  # Exclude the last message which is the current query
                    if isinstance(msg, HumanMessage):
                        chat_history.append(msg)
                    elif isinstance(msg, AIMessage):
                        chat_history.append(msg)
                logger.info(f"Prepared chat history with {len(chat_history)} messages")
            except Exception as chat_error:
                logger.error(f"Error preparing chat history: {chat_error}")
                # Continue without chat history if there's an error
                chat_history = []

            # Run the agent
            logger.info(f"Running agent with query: {query}")
            try:
                agent_result = await self.agent_executor.ainvoke({
                    "input": query,
                    "chat_history": chat_history
                })
                logger.info(f"Agent execution successful")
            except Exception as agent_error:
                logger.error(f"Error executing agent: {agent_error}")
                # If agent fails, continue to response generation
                return {**state, "next_step": "generate_response", "current_step": "generate_response"}

            # Log agent result for debugging
            try:
                # Log a simplified version to avoid huge logs
                log_result = {
                    "output": agent_result.get("output", ""),
                    "has_intermediate_steps": "intermediate_steps" in agent_result,
                    "num_steps": len(agent_result.get("intermediate_steps", [])) if "intermediate_steps" in agent_result else 0
                }
                logger.info(f"Agent result summary: {json.dumps(log_result)}")
            except Exception as log_error:
                logger.error(f"Error logging agent result: {log_error}")

            # Determine next step based on agent's tool usage
            next_step = "generate_response"
            tool_calls = []

            # Check if the agent used any tools
            if "intermediate_steps" in agent_result:
                try:
                    for step in agent_result["intermediate_steps"]:
                        tool_name = step[0].tool
                        tool_input = step[0].tool_input
                        tool_output = step[1]

                        logger.info(f"Tool called: {tool_name} with input: {tool_input}")

                        tool_calls.append({
                            "tool": tool_name,
                            "input": tool_input,
                            "output": tool_output
                        })

                        # Set the next step based on the first tool used
                        if tool_name == "search_competencies" and next_step == "generate_response":
                            next_step = "search_competencies"
                        elif tool_name == "get_user_scores" and next_step == "generate_response":
                            next_step = "get_user_scores"
                except Exception as steps_error:
                    logger.error(f"Error processing intermediate steps: {steps_error}")
                    # If processing steps fails, continue to response generation
                    next_step = "generate_response"

            logger.info(f"Agent determined next step: {next_step}")

            # Update state with agent results
            return {
                **state,
                "next_step": next_step,
                "agent_output": agent_result,
                "tool_calls": tool_calls,
                "current_step": next_step
            }

        except Exception as e:
            logger.error(f"Error in agent tool calling: {e}")
            # In case of any error, proceed to generate response
            return {**state, "next_step": "generate_response", "current_step": "generate_response"}

    async def process_question(self, question: str, conversation_history: Optional[List[Dict[str, str]]] = None,
                             user_name: Optional[str] = None, user_role: Optional[str] = None,
                             user_id: Optional[int] = None) -> str:
        """Process a question using the LangGraph workflow with checkpointing."""
        memory = None
        try:
            logger.info("Starting RAG workflow")
            logger.info(f"Processing question from user: {user_name} (Role: {user_role})")

            # Initialize memory
            memory = ConversationBufferMemory(return_messages=True)

            # Add conversation history to memory if available
            if conversation_history:
                for msg in conversation_history:
                    if msg.get("role") == "user":
                        memory.chat_memory.add_user_message(msg.get("content", ""))
                    elif msg.get("role") == "assistant":
                        memory.chat_memory.add_ai_message(msg.get("content", ""))

            # Add current question to memory
            memory.chat_memory.add_user_message(question)

            # Create a unique ID for this conversation
            conversation_id = f"conv_{user_id or 'anonymous'}_{int(time.time())}"

            initial_state = {
                "memory": memory,
                "query_type": None,
                "search_results": None,
                "user_scores": None,
                "current_step": "classify_query",
                "user_id": user_id,
                "user_name": user_name,
                "user_role": user_role,
                "next_step": None,
                "agent_output": None,
                "tool_calls": None
            }

            # Run the workflow with checkpointing
            final_state = await self.workflow.ainvoke(
                initial_state,
                config={"configurable": {"thread_id": conversation_id}}
            )

            logger.info("RAG workflow completed")

            # Get the last message from memory
            if final_state and "memory" in final_state:
                return final_state["memory"].chat_memory.messages[-1].content
            else:
                raise ValueError("Invalid final state returned from workflow")

        except ValueError as ve:
            logger.error(f"Value error in RAG workflow: {ve}")
            error_message = f"Error en el procesamiento de la consulta: {str(ve)}"
            if memory:
                memory.chat_memory.add_ai_message(error_message)
            return error_message
        except Exception as e:
            logger.error(f"Error in RAG workflow: {e}")
            error_message = f"Lo siento, encontré un error al procesar tu consulta: {str(e)}"
            if memory:
                memory.chat_memory.add_ai_message(error_message)
            return error_message

    async def process_question_stream(self, question: str, conversation_history: Optional[List[Dict[str, str]]] = None,
                                    user_name: Optional[str] = None, user_role: Optional[str] = None,
                                    user_id: Optional[int] = None):
        """Process a question using the LangGraph workflow with streaming and checkpointing."""
        memory = None
        try:
            logger.info("Starting RAG workflow with streaming")
            logger.info(f"Processing question from user: {user_name} (Role: {user_role})")

            # Initialize memory
            memory = ConversationBufferMemory(return_messages=True)

            # Add conversation history to memory if available
            if conversation_history:
                for msg in conversation_history:
                    if msg.get("role") == "user":
                        memory.chat_memory.add_user_message(msg.get("content", ""))
                    elif msg.get("role") == "assistant":
                        memory.chat_memory.add_ai_message(msg.get("content", ""))

            # Add current question to memory
            memory.chat_memory.add_user_message(question)

            # Create a unique ID for this conversation
            conversation_id = f"conv_{user_id or 'anonymous'}_{int(time.time())}"

            initial_state = {
                "memory": memory,
                "query_type": None,
                "search_results": None,
                "user_scores": None,
                "current_step": "classify_query",
                "user_id": user_id,
                "user_name": user_name,
                "user_role": user_role,
                "next_step": None,
                "agent_output": None,
                "tool_calls": None
            }

            # Run the workflow with streaming and checkpointing
            async for state in self.workflow.astream(
                initial_state,
                config={"configurable": {"thread_id": conversation_id}}
            ):
                # Log the current step for debugging
                logger.info(f"Current step: {state.get('current_step')}, Query type: {state.get('query_type')}")

                if state["current_step"] == "generate_response":
                    try:
                        # Check if we have agent output to use directly
                        agent_output = state.get("agent_output")

                        if agent_output and "output" in agent_output:
                            # Use the agent's output directly but stream it chunk by chunk
                            answer = agent_output["output"]
                            logger.info("Using agent's direct output for streaming response")

                            # Simulate streaming by breaking the response into chunks
                            full_response = ""
                            for i in range(0, len(answer), 10):  # Send 10 characters at a time
                                content = answer[i:i+10]
                                full_response += content
                                yield f"data: {content}\n\n"
                                await asyncio.sleep(0.01)  # Small delay to simulate streaming

                            # Add the complete response to memory
                            if state.get("memory"):
                                state["memory"].chat_memory.add_ai_message(full_response)
                        else:
                            # Stream the response using our standard prompt
                            prompt = await self._create_response_prompt(state)

                            response = self.openai_client.chat.completions.create(
                                model=os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
                                messages=[
                                    {"role": "system", "content": "Eres un coach de desarrollo profesional para consultoria de negocios."},
                                    {"role": "user", "content": prompt}
                                ],
                                temperature=0.3,
                                max_tokens=1000,
                                stream=True
                            )

                            full_response = ""
                            async for chunk in response:
                                if chunk.choices[0].delta.content:
                                    content = chunk.choices[0].delta.content
                                    full_response += content
                                    yield f"data: {content}\n\n"

                            # Add the complete response to memory
                            if state.get("memory"):
                                state["memory"].chat_memory.add_ai_message(full_response)

                        logger.info("Streaming response completed")
                    except Exception as e:
                        logger.error(f"Error in response streaming: {e}")
                        error_message = f"Error al generar la respuesta: {str(e)}"
                        if state.get("memory"):
                            state["memory"].chat_memory.add_ai_message(error_message)
                        yield f"data: {error_message}\n\n"
                        break

            # End of stream marker
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"Error in process_question_stream: {e}")
            error_message = f"Lo siento, encontré un error al procesar tu consulta: {str(e)}"

            if memory:
                memory.chat_memory.add_ai_message(error_message)

            yield f"data: {error_message}\n\n"
            yield "data: [DONE]\n\n"