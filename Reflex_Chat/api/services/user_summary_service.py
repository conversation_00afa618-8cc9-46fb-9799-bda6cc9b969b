import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload

from Reflex_Chat.database.models import User, UserProject

logger = logging.getLogger("service.user_summary")

class UserSummaryService:
    def __init__(self, db: Session):
        self.db = db
        logger.info("UserSummaryService initialized")

    def _format_user_data(self, user: User) -> Dict[str, Any]:
        """Format basic user information."""
        logger.debug(f"Formatting user data for user_id: {user.id}")
        return {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "role": user.role.name if user.role else None
        }

    def _format_projects_data(self, project_links: List[UserProject]) -> List[Dict[str, Any]]:
        """Format projects data with duration calculation."""
        logger.debug(f"Formatting {len(project_links)} project links")
        current_date = datetime.utcnow()
        formatted_projects = []

        for link in project_links:
            project = link.project
            if project.status == 'active':
                start_date = project.start_date
                # Calculate months between dates
                months = (current_date.year - start_date.year) * 12 + (current_date.month - start_date.month)
                
                project_data = {
                    "id": project.id,
                    "code": project.code,
                    "name": project.name,
                    "start_date": start_date.isoformat(),
                    "status": project.status,
                    "duration": f"{months} meses"
                }
                formatted_projects.append(project_data)
                logger.debug(f"Formatted project: {project.name} with duration {months} months")

        logger.info(f"Formatted {len(formatted_projects)} active projects")
        return formatted_projects

    def get_user_summary_data(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get complete dashboard data for a user."""
        logger.info(f"Fetching dashboard data for user_id: {user_id}")
        
        user = self.db.exec(
            select(User)
            .where(User.id == user_id)  # Changed from User.user_id to User.id
            .options(
                selectinload(User.role),
                selectinload(User.project_links).selectinload(UserProject.project)
            )
        ).first()

        if not user:
            logger.warning(f"User not found with id: {user_id}")
            return None

        logger.info(f"Found user: {user.name} (ID: {user.id})")
        
        data = {
            "user": self._format_user_data(user),
            "active_projects": self._format_projects_data(user.project_links)
        }
        
        logger.info(f"Returning dashboard data with {len(data['active_projects'])} active projects")
        return data
