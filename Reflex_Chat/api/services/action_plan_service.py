"""
Action Plan Service
==================

This service handles CRUD operations for action plans created by main evaluators.
Action plans are associated with specific factors within evaluations and can only
be created by users with main_evaluator=True.

Usage:
    from Reflex_Chat.api.services.action_plan_service import ActionPlanService
    
    service = ActionPlanService(db_session)
    action_plan = service.create_action_plan(evaluation_id, category, factor, text, evaluator_id)
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlmodel import Session, select
from Reflex_Chat.database.models import (
    ActionPlan, Evaluation, User, CompetencyCategory
)
import logging

logger = logging.getLogger("service.action_plan")

class ActionPlanService:
    """Service for managing action plans."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_action_plan(
        self,
        evaluation_id: int,
        category: CompetencyCategory,
        factor: str,
        action_plan_text: str,
        evaluator_id: int
    ) -> Optional[ActionPlan]:
        """
        Create a new action plan for a specific factor in an evaluation.
        Only main evaluators can create action plans.
        """
        logger.info(f"Creating action plan for evaluation_id={evaluation_id}, factor={factor}, evaluator_id={evaluator_id}")
        
        # Verify the evaluator is a main evaluator
        evaluator = self.db.get(User, evaluator_id)
        if not evaluator or not evaluator.main_evaluator:
            logger.warning(f"User {evaluator_id} is not authorized to create action plans")
            return None
        
        # Verify the evaluation exists
        evaluation = self.db.get(Evaluation, evaluation_id)
        if not evaluation:
            logger.warning(f"Evaluation {evaluation_id} not found")
            return None
        
        # Check if an action plan already exists for this factor
        existing_plan = self.db.exec(
            select(ActionPlan).where(
                ActionPlan.evaluation_id == evaluation_id,
                ActionPlan.category == category,
                ActionPlan.factor == factor
            )
        ).first()
        
        if existing_plan:
            # Update existing action plan
            existing_plan.action_plan_text = action_plan_text
            existing_plan.evaluator_id = evaluator_id
            existing_plan.updated_at = datetime.utcnow()
            self.db.add(existing_plan)
            self.db.commit()
            self.db.refresh(existing_plan)
            logger.info(f"Updated existing action plan with ID: {existing_plan.id}")
            return existing_plan
        else:
            # Create new action plan
            action_plan = ActionPlan(
                evaluation_id=evaluation_id,
                category=category,
                factor=factor,
                action_plan_text=action_plan_text,
                evaluator_id=evaluator_id,
                created_at=datetime.utcnow()
            )
            self.db.add(action_plan)
            self.db.commit()
            self.db.refresh(action_plan)
            logger.info(f"Created new action plan with ID: {action_plan.id}")
            return action_plan
    
    def get_action_plans_for_evaluation(self, evaluation_id: int) -> List[Dict[str, Any]]:
        """Get all action plans for a specific evaluation."""
        logger.info(f"Fetching action plans for evaluation_id={evaluation_id}")
        
        action_plans = self.db.exec(
            select(ActionPlan).where(ActionPlan.evaluation_id == evaluation_id)
        ).all()
        
        result = []
        for plan in action_plans:
            evaluator = self.db.get(User, plan.evaluator_id)
            result.append({
                "id": plan.id,
                "category": plan.category.value,
                "factor": plan.factor,
                "action_plan_text": plan.action_plan_text,
                "evaluator_name": evaluator.name if evaluator else "Unknown",
                "created_at": plan.created_at.isoformat() if plan.created_at else None,
                "updated_at": plan.updated_at.isoformat() if plan.updated_at else None
            })
        
        logger.info(f"Found {len(result)} action plans for evaluation {evaluation_id}")
        return result
    
    def get_action_plans_for_user(
        self,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all action plans for a specific user (evaluatee).
        Returns action plans grouped by factor.
        """
        logger.info(f"Fetching action plans for user_id={user_id}")
        
        # Build query to get evaluations for this user
        eval_query = select(Evaluation).where(Evaluation.evaluatee_id == user_id)
        
        if project_id:
            eval_query = eval_query.where(Evaluation.project_id == project_id)
        if start_date:
            eval_query = eval_query.where(Evaluation.created_at >= start_date)
        if end_date:
            eval_query = eval_query.where(Evaluation.created_at <= end_date)
        
        evaluations = self.db.exec(eval_query).all()
        evaluation_ids = [eval.id for eval in evaluations]
        
        if not evaluation_ids:
            logger.info(f"No evaluations found for user {user_id}")
            return {}
        
        # Get action plans for these evaluations
        action_plans = self.db.exec(
            select(ActionPlan).where(ActionPlan.evaluation_id.in_(evaluation_ids))
        ).all()
        
        # Group by factor
        result = {}
        for plan in action_plans:
            factor_key = f"{plan.category.value}_{plan.factor}"
            if factor_key not in result:
                result[factor_key] = []
            
            evaluator = self.db.get(User, plan.evaluator_id)
            result[factor_key].append({
                "id": plan.id,
                "category": plan.category.value,
                "factor": plan.factor,
                "action_plan_text": plan.action_plan_text,
                "evaluator_name": evaluator.name if evaluator else "Unknown",
                "created_at": plan.created_at.isoformat() if plan.created_at else None,
                "updated_at": plan.updated_at.isoformat() if plan.updated_at else None
            })
        
        logger.info(f"Found action plans for {len(result)} factors for user {user_id}")
        return result
    
    def update_action_plan(
        self,
        action_plan_id: int,
        action_plan_text: str,
        evaluator_id: int
    ) -> Optional[ActionPlan]:
        """Update an existing action plan."""
        logger.info(f"Updating action plan {action_plan_id} by evaluator {evaluator_id}")
        
        # Verify the evaluator is a main evaluator
        evaluator = self.db.get(User, evaluator_id)
        if not evaluator or not evaluator.main_evaluator:
            logger.warning(f"User {evaluator_id} is not authorized to update action plans")
            return None
        
        action_plan = self.db.get(ActionPlan, action_plan_id)
        if not action_plan:
            logger.warning(f"Action plan {action_plan_id} not found")
            return None
        
        action_plan.action_plan_text = action_plan_text
        action_plan.evaluator_id = evaluator_id
        action_plan.updated_at = datetime.utcnow()
        
        self.db.add(action_plan)
        self.db.commit()
        self.db.refresh(action_plan)
        
        logger.info(f"Updated action plan {action_plan_id}")
        return action_plan
    
    def delete_action_plan(self, action_plan_id: int, evaluator_id: int) -> bool:
        """Delete an action plan."""
        logger.info(f"Deleting action plan {action_plan_id} by evaluator {evaluator_id}")
        
        # Verify the evaluator is a main evaluator
        evaluator = self.db.get(User, evaluator_id)
        if not evaluator or not evaluator.main_evaluator:
            logger.warning(f"User {evaluator_id} is not authorized to delete action plans")
            return False
        
        action_plan = self.db.get(ActionPlan, action_plan_id)
        if not action_plan:
            logger.warning(f"Action plan {action_plan_id} not found")
            return False
        
        self.db.delete(action_plan)
        self.db.commit()
        
        logger.info(f"Deleted action plan {action_plan_id}")
        return True
