import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, and_
from sqlalchemy.orm import aliased

from Reflex_Chat.database.models import (
    User, CompetencyScore, CategoryScore, FactorScore,
    Evaluation, Competency, Role, Project, EvaluationQuestion,
    EvaluationAnswer, BehaviorType, ResponseType
)

logger = logging.getLogger("service.user_score")

class UserScoreService:
    # Default weights for roles (can be configured)
    DEFAULT_ROLE_WEIGHTS = {
        "Socio": 0.35,
        "Director": 0.25,
        "Manager": 0.20,
        "Consultor": 0.15,
        "Analista": 0.05
    }

    # Default weights for categories (can be configured)
    DEFAULT_CATEGORY_WEIGHTS = {
        "feedback_cliente": 0.25,
        "feedback_manager": 0.25,
        "competencias_tecnicas": 0.20,
        "competencias_comportamentales": 0.20,
        "aprendizaje": 0.10
    }

    def __init__(self, db: Session):
        self.db = db
        logger.info("UserScoreService initialized")

    def _get_dynamic_weights(
        self,
        available_items: List[str],
        default_weights: Dict[str, float]
    ) -> Dict[str, float]:
        """
        Calculate dynamic weights based on available items.
        Maintains relative proportions while ensuring sum = 1.0
        """
        logger.info(f"Calculating dynamic weights for items: {available_items}")
        logger.info(f"Default weights: {default_weights}")

        # Filter weights for available items
        available_weights = {
            item: default_weights[item]
            for item in available_items
            if item in default_weights
        }
        logger.info(f"Available weights after filtering: {available_weights}")

        if not available_weights:
            # If no predefined weights, distribute evenly
            even_weights = {item: 1.0 / len(available_items) for item in available_items}
            logger.info(f"No predefined weights found, using even distribution: {even_weights}")
            return even_weights

        # Normalize weights to sum to 1.0
        weight_sum = sum(available_weights.values())
        normalized_weights = {k: v / weight_sum for k, v in available_weights.items()}
        logger.info(f"Normalized weights (sum={weight_sum}): {normalized_weights}")

        return normalized_weights

    def _calculate_weighted_competency_score(
        self,
        role_scores: Dict[str, Dict[str, Any]],
        role_weights: Optional[Dict[str, float]] = None
    ) -> float:
        """
        Calculate weighted average score for a competency based on evaluator roles.
        Uses the same weighting logic as the overall score calculation.
        """
        logger.info(f"Calculating weighted score with role_scores: {role_scores}")
        logger.info(f"Using role_weights: {role_weights or self.DEFAULT_ROLE_WEIGHTS}")

        available_roles = list(role_scores.keys())
        logger.info(f"Available roles: {available_roles}")

        # Get dynamic weights based on available roles
        effective_role_weights = self._get_dynamic_weights(
            available_roles,
            role_weights or self.DEFAULT_ROLE_WEIGHTS
        )
        logger.info(f"Effective role weights: {effective_role_weights}")

        # Calculate weighted average
        weighted_score = 0.0
        total_weight = 0.0

        # Log each role's contribution to the weighted score
        for role, data in role_scores.items():
            if data["count"] > 0:  # Only consider roles with data
                role_weight = effective_role_weights.get(role, 0)
                role_contribution = data["score"] * role_weight
                weighted_score += role_contribution
                total_weight += role_weight
                logger.info(f"Role {role}: score={data['score']}, weight={role_weight}, contribution={role_contribution}")
            else:
                logger.info(f"Role {role} has no data (count=0), skipping")

        # Calculate final weighted average
        final_score = weighted_score / total_weight if total_weight > 0 else 0.0
        logger.info(f"Final weighted score: {final_score} (weighted_score={weighted_score}, total_weight={total_weight})")

        return final_score

    def get_competency_scores(
        self,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        role_weights: Optional[Dict[str, float]] = None,
        evaluator_role: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get average competency scores with breakdowns."""
        evaluator = aliased(User, name="evaluator")
        evaluatee = aliased(User, name="evaluatee")

        # First, get the scores and counts
        query = (
            select(
                Competency.id,
                Competency.name,
                Competency.description,
                Competency.category,
                Competency.factor,  # Add factor field
                Role.name.label("evaluator_role"),
                func.avg(CompetencyScore.score).label("avg_score"),
                func.count(CompetencyScore.id).label("count")
            )
            .join(CompetencyScore.evaluation)
            .join(CompetencyScore.competency)
            .join(evaluatee, Evaluation.evaluatee_id == evaluatee.id)
            .join(evaluator, Evaluation.evaluator_id == evaluator.id)
            .join(Role, evaluator.role_id == Role.id)
            .where(evaluatee.id == user_id)
            # Only include evaluations where evaluatee_role_id matches current role_id
            .where(Evaluation.evaluatee_role_id == evaluatee.role_id)
        )

        if project_id:
            query = query.where(Evaluation.project_id == project_id)
        if start_date:
            query = query.where(Evaluation.created_at >= start_date)
        if end_date:
            query = query.where(Evaluation.created_at <= end_date)
        if evaluator_role:
            query = query.where(Role.name == evaluator_role)

        results = self.db.exec(
            query.group_by(
                Competency.id,
                Competency.name,
                Competency.description,
                Competency.category,
                Competency.factor,  # Add factor to group_by
                Role.name
            )
        ).all()

        # Organize results by competency
        competency_scores = {}
        for r in results:
            if r[0] not in competency_scores:
                competency_scores[r[0]] = {
                    "name": r[1],
                    "description": r[2],
                    "category": r[3],
                    "factor": r[4] or "General",  # Add factor field with default value
                    "by_role": {
                        role: {"score": 0, "count": 0}
                        for role in self.DEFAULT_ROLE_WEIGHTS.keys()
                    },
                    "comments": []
                }

            # Update role-based scores
            role = r[5]  # Updated index for evaluator_role
            if role not in competency_scores[r[0]]["by_role"]:
                competency_scores[r[0]]["by_role"][role] = {"score": 0, "count": 0}
            competency_scores[r[0]]["by_role"][role] = {
                "score": float(r[6]),  # Updated index for avg_score
                "count": int(r[7])  # Updated index for count
            }

        # Now get comments for each competency
        for comp_id in competency_scores.keys():
            comments_query = (
                select(
                    CompetencyScore.comments,
                    User.name.label("evaluator_name"),
                    Role.name.label("evaluator_role"),
                    Evaluation.evaluator_type
                )
                .join(CompetencyScore.evaluation)
                .join(User, Evaluation.evaluator_id == User.id)
                .join(Role, User.role_id == Role.id)
                .where(
                    CompetencyScore.competency_id == comp_id,
                    Evaluation.evaluatee_id == user_id,
                    CompetencyScore.comments != None,
                    CompetencyScore.comments != ""
                )
            )

            if project_id:
                comments_query = comments_query.where(Evaluation.project_id == project_id)
            if start_date:
                comments_query = comments_query.where(Evaluation.created_at >= start_date)
            if end_date:
                comments_query = comments_query.where(Evaluation.created_at <= end_date)

            comments_results = self.db.exec(comments_query).all()

            for comment in comments_results:
                if comment[0]:  # If comment is not None or empty
                    competency_scores[comp_id]["comments"].append({
                        "text": comment[0],
                        "evaluator": comment[1],
                        "role": comment[2],
                        "type": comment[3]
                    })

            # Calculate weighted average score for each competency
            role_data = {
                role: data
                for role, data in competency_scores[comp_id]["by_role"].items()
                if data["count"] > 0
            }
            if role_data:
                competency_scores[comp_id]["weighted_score"] = self._calculate_weighted_competency_score(
                    role_data, role_weights
                )
            else:
                competency_scores[comp_id]["weighted_score"] = 0.0

        return competency_scores

    def get_factor_scores(
        self,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        role_weights: Optional[Dict[str, float]] = None,
        evaluator_role: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get average factor scores with breakdowns."""
        logger.info(f"Getting factor scores for user_id={user_id}, project_id={project_id}, evaluator_role={evaluator_role}")
        logger.info(f"Using role_weights: {role_weights or self.DEFAULT_ROLE_WEIGHTS}")

        evaluator = aliased(User, name="evaluator")
        evaluatee = aliased(User, name="evaluatee")

        query = (
            select(
                FactorScore.factor,
                FactorScore.category,
                Role.name.label("evaluator_role"),
                func.avg(FactorScore.score).label("avg_score"),
                func.count(FactorScore.id).label("count")
            )
            .join(FactorScore.evaluation)
            .join(evaluatee, Evaluation.evaluatee_id == evaluatee.id)
            .join(evaluator, Evaluation.evaluator_id == evaluator.id)
            .join(Role, evaluator.role_id == Role.id)
            .where(evaluatee.id == user_id)
            # Only include evaluations where evaluatee_role_id matches current role_id
            .where(Evaluation.evaluatee_role_id == evaluatee.role_id)
        )

        if project_id:
            query = query.where(Evaluation.project_id == project_id)
        if start_date:
            query = query.where(Evaluation.created_at >= start_date)
        if end_date:
            query = query.where(Evaluation.created_at <= end_date)
        if evaluator_role:
            query = query.where(Role.name == evaluator_role)

        logger.info(f"Executing factor scores query: {str(query)}")
        results = self.db.exec(
            query.group_by(
                FactorScore.factor,
                FactorScore.category,
                Role.name
            )
        ).all()

        logger.info(f"Factor scores raw results count: {len(results)}")
        for i, r in enumerate(results):
            logger.info(f"Factor result {i}: factor={r[0]}, category={r[1]}, role={r[2]}, score={r[3]}, count={r[4]}")

        # Log specific results for Competencias comportamentales
        comp_comportamentales_results = [r for r in results if r[1] == "COMPETENCIAS_COMPORTAMENTALES"]
        logger.info(f"Found {len(comp_comportamentales_results)} results for COMPETENCIAS_COMPORTAMENTALES category")
        for i, r in enumerate(comp_comportamentales_results):
            logger.info(f"COMP_COMPORT factor result {i}: factor={r[0]}, category={r[1]}, role={r[2]}, score={r[3]}, count={r[4]}")

        return self._organize_factor_results(results, role_weights)

    def get_category_scores(
        self,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        role_weights: Optional[Dict[str, float]] = None,
        evaluator_role: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get average category scores with breakdowns by evaluator type and role."""
        logger.info(f"Getting category scores for user_id={user_id}, project_id={project_id}, evaluator_role={evaluator_role}")
        logger.info(f"Using role_weights: {role_weights or self.DEFAULT_ROLE_WEIGHTS}")

        evaluator = aliased(User, name="evaluator")
        evaluatee = aliased(User, name="evaluatee")

        query = (
            select(
                CategoryScore.category,
                Role.name.label("evaluator_role"),
                func.avg(CategoryScore.score).label("avg_score"),
                func.count(CategoryScore.id).label("count")
            )
            .join(CategoryScore.evaluation)
            .join(evaluator, Evaluation.evaluator_id == evaluator.id)
            .join(Role, evaluator.role_id == Role.id)
            .join(evaluatee, Evaluation.evaluatee_id == evaluatee.id)
            .where(evaluatee.id == user_id)
            # Only include evaluations where evaluatee_role_id matches current role_id
            .where(Evaluation.evaluatee_role_id == evaluatee.role_id)
        )

        # Apply filters
        if project_id:
            query = query.where(Evaluation.project_id == project_id)
        if start_date:
            query = query.where(Evaluation.created_at >= start_date)
        if end_date:
            query = query.where(Evaluation.created_at <= end_date)
        if evaluator_role:
            query = query.where(Role.name == evaluator_role)

        logger.info(f"Executing category scores query: {str(query)}")
        results = self.db.exec(
            query.group_by(
                CategoryScore.category,
                Role.name
            )
        ).all()

        logger.info(f"Category scores raw results count: {len(results)}")
        for i, r in enumerate(results):
            logger.info(f"Category result {i}: category={r[0]}, role={r[1]}, score={r[2]}, count={r[3]}")

        # Log specific results for Competencias comportamentales
        comp_comportamentales_results = [r for r in results if r[0] == "COMPETENCIAS_COMPORTAMENTALES"]
        logger.info(f"Found {len(comp_comportamentales_results)} results for COMPETENCIAS_COMPORTAMENTALES category")
        for i, r in enumerate(comp_comportamentales_results):
            logger.info(f"COMP_COMPORT category result {i}: category={r[0]}, role={r[1]}, score={r[2]}, count={r[3]}")

        return self._organize_category_results(results, role_weights)

    def get_overall_score(
        self,
        user_id: int,
        role_weights: Optional[Dict[str, float]] = None,
        category_weights: Optional[Dict[str, float]] = None,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        evaluator_role: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate overall weighted score based on categories and evaluator roles.
        Returns both the final score and the intermediate calculations.

        Note: This method groups data by category and role only, without considering evaluator_type.
        All other score calculation methods now use the same approach for consistency.
        """
        evaluator = aliased(User, name="evaluator")
        evaluatee = aliased(User, name="evaluatee")

        query = (
            select(
                CategoryScore.category,
                Role.name.label("evaluator_role"),
                func.avg(CategoryScore.score).label("avg_score"),
                func.count(CategoryScore.id).label("count")
            )
            .join(CategoryScore.evaluation)
            .join(evaluator, Evaluation.evaluator_id == evaluator.id)
            .join(Role, evaluator.role_id == Role.id)
            .join(evaluatee, Evaluation.evaluatee_id == evaluatee.id)
            .where(evaluatee.id == user_id)
            # Only include evaluations where evaluatee_role_id matches current role_id
            .where(Evaluation.evaluatee_role_id == evaluatee.role_id)
        )

        if project_id:
            query = query.where(Evaluation.project_id == project_id)
        if start_date:
            query = query.where(Evaluation.created_at >= start_date)
        if end_date:
            query = query.where(Evaluation.created_at <= end_date)
        if evaluator_role:
            query = query.where(Role.name == evaluator_role)

        results = self.db.exec(
            query.group_by(
                CategoryScore.category,
                Role.name
            )
        ).all()

        # Organize results by category and role
        scores_by_category = {}
        available_roles = set()
        available_categories = set()

        for r in results:
            category = r[0]
            role = r[1]
            score = float(r[2])
            count = int(r[3])

            available_roles.add(role)
            available_categories.add(category)

            if category not in scores_by_category:
                scores_by_category[category] = {}
            scores_by_category[category][role] = {"score": score, "count": count}

        # Get dynamic weights
        effective_role_weights = self._get_dynamic_weights(
            list(available_roles),
            role_weights or self.DEFAULT_ROLE_WEIGHTS
        )

        effective_category_weights = self._get_dynamic_weights(
            list(available_categories),
            category_weights or self.DEFAULT_CATEGORY_WEIGHTS
        )

        # Calculate weighted scores
        category_scores = {}
        final_score = 0.0

        for category, role_scores in scores_by_category.items():
            # Calculate weighted average for this category across roles
            category_score = 0.0
            total_role_weight = 0.0

            for role, data in role_scores.items():
                role_weight = effective_role_weights.get(role, 0)
                category_score += data["score"] * role_weight
                total_role_weight += role_weight

            if total_role_weight > 0:
                category_score /= total_role_weight
                category_scores[category] = category_score
                final_score += category_score * effective_category_weights.get(category, 0)

        return {
            "final_score": final_score,
            "category_scores": category_scores,
            "role_weights_used": effective_role_weights,
            "category_weights_used": effective_category_weights,
            "raw_data": scores_by_category
        }

    def get_user_score_data_summary(
        self,
        user_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        role_weights: Optional[Dict[str, float]] = None,
        category_weights: Optional[Dict[str, float]] = None,
        evaluator_role: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Get complete score data for a user."""
        return {
            "overall_score": self.get_overall_score(
                user_id,
                role_weights=role_weights,
                category_weights=category_weights,
                project_id=project_id,
                start_date=start_date,
                end_date=end_date,
                evaluator_role=evaluator_role
            ),
            "competency_scores": self.get_competency_scores(
                user_id,
                project_id,
                start_date,
                end_date,
                role_weights=role_weights,
                evaluator_role=evaluator_role
            ),
            "factor_scores": self.get_factor_scores(
                user_id,
                project_id,
                start_date,
                end_date,
                role_weights=role_weights,
                evaluator_role=evaluator_role
            ),
            "category_scores": self.get_category_scores(user_id, project_id, start_date, end_date, role_weights=role_weights, evaluator_role=evaluator_role)
        }

    def _organize_factor_results(self, results, role_weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Helper method to organize factor score results."""
        logger.info(f"Organizing factor results with role_weights: {role_weights}")

        factor_scores = {}
        for r in results:
            factor_key = f"{r[0]}_{r[1]}"  # Combine factor and category
            if factor_key not in factor_scores:
                factor_scores[factor_key] = {
                    "factor": r[0],
                    "category": r[1],
                    "by_role": {
                        role: {"score": 0, "count": 0}
                        for role in self.DEFAULT_ROLE_WEIGHTS.keys()
                    }
                }
                logger.info(f"Created new factor entry for {factor_key}: factor={r[0]}, category={r[1]}")

            # Update role-based scores
            role = r[2]
            if role not in factor_scores[factor_key]["by_role"]:
                factor_scores[factor_key]["by_role"][role] = {"score": 0, "count": 0}
            factor_scores[factor_key]["by_role"][role] = {
                "score": float(r[3]),
                "count": int(r[4])
            }
            logger.info(f"Updated {factor_key} role-based score for {role}: score={float(r[3])}, count={int(r[4])}")

        # Calculate weighted average score for each factor
        for factor_key in factor_scores:
            role_data = {
                role: data
                for role, data in factor_scores[factor_key]["by_role"].items()
                if data["count"] > 0
            }
            logger.info(f"Calculating weighted score for {factor_key} with role_data: {role_data}")

            if role_data:
                weighted_score = self._calculate_weighted_competency_score(
                    role_data, role_weights
                )
                factor_scores[factor_key]["weighted_score"] = weighted_score
                logger.info(f"Factor {factor_key} weighted_score calculated: {weighted_score}")
            else:
                factor_scores[factor_key]["weighted_score"] = 0.0
                logger.info(f"Factor {factor_key} has no role data, setting weighted_score to 0.0")

            # Log specific details for Competencias comportamentales
            if factor_scores[factor_key]["category"] == "COMPETENCIAS_COMPORTAMENTALES":
                logger.info(f"COMP_COMPORT factor {factor_key} final weighted_score: {factor_scores[factor_key]['weighted_score']}")
                logger.info(f"COMP_COMPORT factor {factor_key} role_data: {role_data}")
                logger.info(f"COMP_COMPORT factor calculation method: Grouped by factor, category and role only")

        return factor_scores

    def get_competency_questions_by_behavior(
        self,
        user_id: int,
        competency_id: int,
        project_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        evaluator_role: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get competency questions grouped by behavior type with response counts.

        Returns a dictionary with:
        - competency_id: The ID of the competency
        - questions_by_behavior: Dictionary with behavior types as keys and lists of questions as values
          Each question includes:
          - id: Question ID
          - text: Question text
          - response_counts: Dictionary with response types as keys and counts as values
        """
        logger.info(f"Getting competency questions for user_id={user_id}, competency_id={competency_id}")

        # First, get all questions for this competency grouped by behavior type
        questions_query = (
            select(
                EvaluationQuestion.id,
                EvaluationQuestion.text,
                EvaluationQuestion.behavior_type
            )
            .where(EvaluationQuestion.competency_id == competency_id)
            .order_by(EvaluationQuestion.behavior_type, EvaluationQuestion.id)
        )

        questions = self.db.exec(questions_query).all()

        if not questions:
            logger.info(f"No questions found for competency_id={competency_id}")
            return {
                "competency_id": str(competency_id),
                "questions_by_behavior": {}
            }

        # Get all question IDs to use in the answers query
        question_ids = [q[0] for q in questions]

        # Build query to get response counts for each question
        evaluator = aliased(User, name="evaluator")
        evaluatee = aliased(User, name="evaluatee")

        answers_query = (
            select(
                EvaluationAnswer.question_id,
                EvaluationAnswer.response,
                func.count(EvaluationAnswer.id).label("count")
            )
            .join(EvaluationAnswer.evaluation)
            .join(evaluatee, Evaluation.evaluatee_id == evaluatee.id)
            .join(evaluator, Evaluation.evaluator_id == evaluator.id)
            .join(Role, evaluator.role_id == Role.id)
            .where(
                evaluatee.id == user_id,
                EvaluationAnswer.question_id.in_(question_ids),
                # Only include evaluations where evaluatee_role_id matches current role_id
                Evaluation.evaluatee_role_id == evaluatee.role_id
            )
            .group_by(
                EvaluationAnswer.question_id,
                EvaluationAnswer.response
            )
        )

        # Apply filters
        if project_id:
            answers_query = answers_query.where(Evaluation.project_id == project_id)
        if start_date:
            answers_query = answers_query.where(Evaluation.created_at >= start_date)
        if end_date:
            answers_query = answers_query.where(Evaluation.created_at <= end_date)
        if evaluator_role:
            answers_query = answers_query.where(Role.name == evaluator_role)

        answers = self.db.exec(answers_query).all()

        # Organize response counts by question ID
        response_counts_by_question = {}
        for answer in answers:
            question_id, response, count = answer
            if question_id not in response_counts_by_question:
                response_counts_by_question[question_id] = {}
            response_counts_by_question[question_id][response] = count

        # Organize questions by behavior type
        questions_by_behavior = {}
        for question in questions:
            question_id, text, behavior_type = question

            if behavior_type not in questions_by_behavior:
                questions_by_behavior[behavior_type] = []

            # Get response counts for this question
            response_counts = response_counts_by_question.get(question_id, {})

            # Ensure all response types are represented with string keys for consistent JSON serialization
            all_response_counts = {
                "si": response_counts.get(ResponseType.SI, 0),
                "no": response_counts.get(ResponseType.NO, 0),
                "a_veces": response_counts.get(ResponseType.A_VECES, 0)
            }

            questions_by_behavior[behavior_type].append({
                "id": str(question_id),
                "text": text,
                "response_counts": all_response_counts
            })

        return {
            "competency_id": str(competency_id),
            "questions_by_behavior": questions_by_behavior
        }

    def _organize_category_results(self, results, role_weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Helper method to organize category score results with role breakdowns."""
        logger.info(f"Organizing category results with role_weights: {role_weights}")

        category_scores = {}
        for r in results:
            if r[0] not in category_scores:
                category_scores[r[0]] = {
                    "by_role": {
                        role: {"score": 0, "count": 0}
                        for role in self.DEFAULT_ROLE_WEIGHTS.keys()
                    }
                }
                logger.info(f"Created new category entry for {r[0]}")

            # Update role-based scores
            role = r[1]
            if role not in category_scores[r[0]]["by_role"]:
                category_scores[r[0]]["by_role"][role] = {"score": 0, "count": 0}
            category_scores[r[0]]["by_role"][role] = {
                "score": float(r[2]),
                "count": int(r[3])
            }
            logger.info(f"Updated {r[0]} role-based score for {role}: score={float(r[2])}, count={int(r[3])}")

        # Calculate weighted average score for each category
        for category in category_scores:
            role_data = {
                role: data
                for role, data in category_scores[category]["by_role"].items()
                if data["count"] > 0
            }
            logger.info(f"Calculating weighted score for category {category} with role_data: {role_data}")

            if role_data:
                weighted_score = self._calculate_weighted_competency_score(
                    role_data, role_weights
                )
                category_scores[category]["weighted_score"] = weighted_score
                logger.info(f"Category {category} weighted_score calculated: {weighted_score}")
            else:
                category_scores[category]["weighted_score"] = 0.0
                logger.info(f"Category {category} has no role data, setting weighted_score to 0.0")

            # Log specific details for Competencias comportamentales
            if category == "COMPETENCIAS_COMPORTAMENTALES":
                logger.info(f"COMP_COMPORT category final weighted_score: {category_scores[category]['weighted_score']}")
                logger.info(f"COMP_COMPORT category role_data: {role_data}")
                logger.info(f"COMP_COMPORT category calculation method: Grouped by category and role only")

        return category_scores
