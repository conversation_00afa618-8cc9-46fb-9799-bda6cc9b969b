# Reflex Chat Application

A chat application built with Reflex, FastAPI, PostgreSQL, and Redis that provides competency evaluation and RAG-based conversational capabilities.

## Prerequisites

- Docker and Docker Compose
- Git

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd Reflex-Chat
```

### 2. Environment Configuration

1. Copy the example environment file to create your own:

```bash
cp .env.example .env
```

2. Edit the `.env` file and fill in the required values:
   - Authentication credentials (Azure AD)
   - OpenAI API key
   - Azure Search configuration (if using)
   - Database credentials

### 3. Start the Application with Docker

The application uses Docker Compose to manage all services. To start everything:

```bash
docker-compose up -d
```

This will start the following services:
- PostgreSQL database
- Redis cache
- Reflex application (frontend + backend)
- FastAPI service

### 4. Access the Application

Once all containers are running, you can access:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- FastAPI service: http://localhost:8001

### 5. Database Initialization

The database will be automatically initialized on first startup. If you need to reset the database:

```bash
# Connect to the Reflex container
docker-compose exec reflex bash

# Inside the container, run:
python -c "from Reflex_Chat.database.db import reset_db_and_tables; reset_db_and_tables()"
```

## Application Structure

- `Reflex_Chat/`: Main application code
  - `api/`: FastAPI service for RAG and scoring
  - `components/`: Reusable UI components
  - `database/`: Database models and utilities
  - `pages/`: Application pages and routes
- `assets/`: Static assets and data files
- `docker-compose.yml`: Docker configuration for development
- `compose.prod.yml`: Docker configuration for production

## Development Workflow

For development, you can use the following commands:

```bash
# Restart all services
docker-compose down && docker-compose up -d

# View logs
docker-compose logs -f

# Run a specific service
docker-compose up -d reflex

```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Check that PostgreSQL container is running: `docker-compose ps`
   - Verify database credentials in `.env` file

2. **API Connection Issues**:
   - Ensure FastAPI service is running: `docker-compose logs fastapi`
   - Check API URLs in `.env` file

3. **Authentication Problems**:
   - Verify Azure AD credentials in `.env` file
   - Check redirect URI configuration in Azure portal

### Logs

To check logs for a specific service:

```bash
docker-compose logs -f [service_name]
```

Where `[service_name]` can be: `postgres`, `redis`, `reflex`, or `fastapi`.

## Contact

For any questions or issues, please contact the repository owner.
