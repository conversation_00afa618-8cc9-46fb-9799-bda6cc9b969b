version: "3.9"

services:
  postgres:
    image: postgres:15
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    restart: always

  reflex:
    build:
      context: .
      dockerfile: Dockerfile
    command: reflex run --env prod
    env_file:
      - .env
    depends_on:
      - redis
      - postgres
    restart: always

  web:
    build:
      context: .
      dockerfile: web.Dockerfile
    ports:
      - "80:80"
    depends_on:
      - reflex
    restart: always

volumes:
  postgres_data: 