FROM python:3.11-slim

# Set working directory inside container
WORKDIR /app

# Install required system packages
RUN apt-get update && apt-get install -y gcc libpq-dev curl unzip

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy your local code
COPY . .

# Build the frontend for production deployments
RUN reflex init

# Default command for development
CMD ["reflex", "run", "--env", "dev"]
