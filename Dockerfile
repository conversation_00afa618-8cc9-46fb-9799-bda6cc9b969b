FROM python:3.11-slim

# Set working directory inside container
WORKDIR /app

# Install required system packages
RUN apt-get update && apt-get install -y gcc libpq-dev curl unzip \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy your local code
COPY . .

# Set environment variables for Railway deployment
ENV PYTHONUNBUFFERED=1
ENV HOST=0.0.0.0
ENV REFLEX_FRONTEND_HOST=0.0.0.0
ENV REFLEX_BACKEND_HOST=0.0.0.0

# Make startup script executable
RUN chmod +x start.sh

# Build the frontend for production deployments
RUN reflex init

# Expose the primary port
EXPOSE 3000

# Health check with longer timeout
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Use startup script for better debugging
CMD ["./start.sh"]
