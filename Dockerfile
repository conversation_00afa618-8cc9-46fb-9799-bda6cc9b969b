FROM python:3.11-slim

# Set working directory inside container
WORKDIR /app

# Install required system packages and clean up
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    unzip \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Python dependencies with memory optimization
COPY requirements.txt .
RUN pip install --no-cache-dir --disable-pip-version-check -r requirements.txt \
    && pip cache purge

# Copy your local code
COPY . .

# Set memory-optimized environment variables
ENV NODE_OPTIONS="--max-old-space-size=1024"
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONOPTIMIZE=1

# Build the frontend for production deployments
RUN reflex init

# Clean up build artifacts to save memory
RUN find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true \
    && find . -type f -name "*.pyc" -delete 2>/dev/null || true

# Expose ports
EXPOSE 3000 8000

# Health check with longer timeout for Railway
HEALTHCHECK --interval=60s --timeout=30s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Default command for production
CMD ["reflex", "run", "--env", "prod"]
