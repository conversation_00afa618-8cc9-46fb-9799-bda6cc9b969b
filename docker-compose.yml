version: "3.9"

services:
  postgres:
    image: postgres:15
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  reflex:
    build:
      context: .
      dockerfile: Dockerfile
    command: reflex run --env dev
    ports:
      - "3000:3000"  # Frontend
      - "8000:8000"  # Backend
    volumes:
      - .:/app
    env_file:
      - .env
    environment:
      - FASTAPI_URL=http://fastapi:8001
      - API_URL=http://localhost:8000
      - WEBSOCKET_URL=ws://localhost:8000
      - FRONTEND_PORT=3000
      - BACKEND_PORT=8000
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      fastapi:
        condition: service_started

  fastapi:
    build:
      context: ./Reflex_Chat/api
      dockerfile: Dockerfile.fastapi
    ports:
      - "8001:8001"
    env_file:
      - .env
    volumes:
      - ./Reflex_Chat:/app/Reflex_Chat
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      bash -c "pip install openai aiohttp &&
               uvicorn main:app --host 0.0.0.0 --port 8001 --reload"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
