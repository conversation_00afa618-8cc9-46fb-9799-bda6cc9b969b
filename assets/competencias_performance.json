[{"code": "35", "name": "Destreza tecnológica", "description": "Prever y adoptar innovaciones en el negocio creando aplicaciones digitales y tecnológicas. En consultoría, esto implica identificar oportunidades tecnológicas para optimizar procesos, mejorar la eficiencia y ofrecer soluciones innovadoras a los clientes.", "categoria": "competencias_comportamentales", "factor": "Competencias Comportamentales", "group": "Comprensión del negocio", "source": "KFLA", "role": ["<PERSON><PERSON><PERSON>", "Consultor"], "expert_behaviour": ["Sabe prever el impacto de las tecnologías emergentes y hace ajustes estratégicos en consecuencia.", "Explora el entorno en busca de nuevas habilidades técnicas, conocimientos o capacidades que pueden beneficiar el negocio o el desempeño personal.", "Rechaza las tecnologías de bajo impacto o pasajeras, enfocándose en aquellas con aplicaciones estratégicas.", "Aprende fácilmente y adopta nuevas tecnologías, asegurando una integración efectiva en el trabajo y los procesos organizacionales."], "talented_behaviour": ["Identifica rápidamente qué tecnologías tienen potencial para transformar procesos de negocio y prioriza su aprendizaje.", "Experimenta con nuevas herramientas digitales y comparte aprendizajes con el equipo de manera proactiva.", "Adapta soluciones tecnológicas existentes a nuevos contextos con creatividad y eficiencia."], "low_skill_behaviour": ["Se resiste a adoptar nuevas tecnologías, prefiriendo mantener métodos tradicionales aunque sean menos eficientes.", "Invierte tiempo en aprender herramientas sin evaluar previamente su relevancia estratégica para el negocio.", "Muestra dificultad para transferir conocimientos tecnológicos entre diferentes contextos o proyectos."], "introducción_al_factor": "Las competencias comportamentales son fundamentales para el éxito en consultoría, ya que determinan cómo interactuamos con clientes, colegas y stakeholders. Estas competencias nos permiten construir relaciones sólidas, comunicar efectivamente y mantener un alto nivel de profesionalismo en todas nuestras interacciones."}, {"code": "6", "name": "<PERSON><PERSON><PERSON>", "description": "Crear asociaciones y trabajar en colaboración con otras personas para alcanzar objetivos compartidos. La colaboración efectiva no solo impulsa el logro de metas, sino que también fortalece la confianza, la creatividad y la eficiencia dentro de los equipos. En consultoría, esto implica coordinar esfuerzos con clientes y equipos multidisciplinarios para generar soluciones integrales y alineadas con los objetivos del negocio.", "categoria": "competencias_comportamentales", "factor": "Competencias Comportamentales", "group": "Establecer relaciones de colaboración", "source": "KFLA", "role": ["<PERSON><PERSON><PERSON>", "Consultor"], "expert_behaviour": ["Trabaja en cooperación con personas a través de la organización para alcanzar objetivos compartidos, promoviendo la alineación entre equipos y áreas de negocio.", "Representa sus propios intereses, siendo al mismo tiempo justo con los demás y sus áreas, asegurando una toma de decisiones equilibrada.", "Se asocia con otros para hacer las cosas, gestionando eficientemente la dinámica de equipos en proyectos transversales.", "Da crédito a los demás por sus contribuciones y logros, fortaleciendo la confianza y la motivación del equipo.", "Obtiene la confianza y el apoyo de otras personas, facilitando la construcción de relaciones productivas a largo plazo."], "talented_behaviour": ["Identifica oportunidades de sinergia entre diferentes equipos y facilita conexiones que generan valor añadido.", "Construye puentes entre perspectivas divergentes, encontrando puntos comunes que permiten avanzar en situaciones complejas.", "Fomenta un ambiente de colaboración donde todos se sienten cómodos compartiendo ideas y asumiendo riesgos calculados."], "low_skill_behaviour": ["Prioriza consistentemente sus propios objetivos por encima de los del equipo o la organización.", "Muestra resistencia a compartir información o recursos que podrían beneficiar a otros equipos.", "<PERSON><PERSON>ta as<PERSON>r responsabilidad en tareas colaborativas, especialmente cuando requieren esfuerzo adicional."], "introducción_al_factor": "Las competencias comportamentales son fundamentales para el éxito en consultoría, ya que determinan cómo interactuamos con clientes, colegas y stakeholders. Estas competencias nos permiten construir relaciones sólidas, comunicar efectivamente y mantener un alto nivel de profesionalismo en todas nuestras interacciones."}, {"code": "7", "name": "Comunica efectivamente", "description": "Desarrollar y generar comunicaciones multimodales que transmitan una clara comprensión de las necesidades exclusivas de diferentes audiencias. La comunicación eficaz es clave para alinear equipos, inspirar acción y garantizar la claridad en la toma de decisiones. En consultoría, esto implica transmitir ideas complejas de manera comprensible, adaptar el mensaje a distintos stakeholders y lograr influencia a través de la comunicación estratégica.", "categoria": "competencias_comportamentales", "factor": "Competencias Comportamentales", "group": "Influenciar a las personas", "source": "KFLA", "role": ["<PERSON><PERSON><PERSON>", "Consultor"], "expert_behaviour": ["Es efectivo en entornos variados de comunicación: comunicaciones personales, en grupos pequeños y grandes o entre diversos estilos y posiciones, asegurando claridad y alineación en proyectos clave.", "Escucha a los demás con atención, demostrando empatía y adaptando su respuesta en función de las necesidades del interlocutor.", "Se ajusta de acuerdo a la audiencia y el mensaje, estructurando la información para optimizar el impacto en cada contexto.", "Proporciona información oportuna y útil a otras personas de la organización, facilitando la toma de decisiones informadas.", "Promueve la expresión de ideas y opiniones diversas, fomentando un ambiente de debate constructivo."], "talented_behaviour": ["Traduce conceptos complejos en mensajes claros y accesibles, adaptados perfectamente a diferentes audiencias.", "Detecta sutilezas en la comunicación no verbal y ajusta su enfoque para mantener conversaciones productivas.", "Utiliza preguntas estratégicas para guiar discusiones hacia conclusiones constructivas y consensuadas."], "low_skill_behaviour": ["Utiliza un único estilo de comunicación independientemente de la audiencia o el contexto.", "Interrumpe frecuentemente o muestra impaciencia cuando otros expresan ideas diferentes a las suyas.", "Presenta información de manera desorganizada o excesivamente técnica, dificultando su comprensión."], "introducción_al_factor": "Las competencias comportamentales son fundamentales para el éxito en consultoría, ya que determinan cómo interactuamos con clientes, colegas y stakeholders. Estas competencias nos permiten construir relaciones sólidas, comunicar efectivamente y mantener un alto nivel de profesionalismo en todas nuestras interacciones."}, {"code": "1", "name": "Asegura responsabilidad", "description": "Hacerse a sí mismo y a los demás responsables del cumplimiento de los compromisos. Asumir la responsabilidad de las decisiones y garantizar que las personas cumplan con sus obligaciones es esencial en cualquier entorno profesional. En este campo, implica liderar con el ejemplo, asegurar la ejecución efectiva de proyectos y garantizar que las recomendaciones estratégicas se implementen correctamente.", "categoria": "competencias_comportamentales", "factor": "Competencias Comportamentales", "group": "Enfoque en el desempeño", "source": "KFLA", "role": ["<PERSON><PERSON><PERSON>", "Consultor"], "expert_behaviour": ["Cumple sus compromisos y se asegura de que los demás hagan lo mismo, garantizando la ejecución oportuna de entregables clave.", "Actúa con claro sentido de pertenencia, alineándose con los objetivos del cliente y asumiendo su éxito como propio.", "Asume la responsabilidad personal por las decisiones, las acciones y los fracasos, reconociendo errores rápidamente y proponiendo soluciones efectivas.", "Establece responsabilidades y procesos claros para supervisar el trabajo y medir los resultados, definiendo métricas de éxito para proyectos y clientes.", "Diseña mecanismos de retroalimentación en el trabajo, implementando sesiones estructuradas de revisión y mejora continua."], "talented_behaviour": ["Anticipa obstáculos potenciales y desarrolla planes de contingencia para asegurar el cumplimiento de compromisos.", "Crea sistemas de seguimiento que promueven la responsabilidad compartida y la transparencia en el equipo.", "Aborda proactivamente situaciones difíciles, asumiendo responsabilidad incluso cuando no está directamente involucrado."], "low_skill_behaviour": ["<PERSON><PERSON>ta as<PERSON>r responsabilidad cuando los resultados no son los esperados, buscando justificaciones externas.", "Establece compromisos poco realistas y luego falla en cumplirlos, afectando la confianza del equipo.", "Supervisa el trabajo de forma inconsistente, sin establecer expectativas claras o mecanismos de seguimiento."], "introducción_al_factor": "Las competencias comportamentales son fundamentales para el éxito en consultoría, ya que determinan cómo interactuamos con clientes, colegas y stakeholders. Estas competencias nos permiten construir relaciones sólidas, comunicar efectivamente y mantener un alto nivel de profesionalismo en todas nuestras interacciones."}, {"code": "150", "name": "Conocimientos de Management", "description": "Demuestra un sólido entendimiento de los conceptos clave de management y su impacto en el desempeño financiero y operativo.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Comprende los conceptos clave de negocio y su impacto en resultados: ingresos, costes, margen, rentabilidad, pricing, productividad, etc.", "Interpreta correctamente relaciones básicas entre indicadores (por ejemplo: ingresos = precio × volumen; beneficio = ingresos – costes).", "Aplica conceptos de distintas áreas de forma operativa en el análisis: marketing (segmentación, propuesta de valor), finanzas (EBITDA, ROI), operaciones (stock, lead time).", "Pregunta con criterio si no entiende un concepto técnico, buscando asegurar precisión y coherencia."], "talented_behaviour": ["Conecta rápidamente conceptos de management de diferentes disciplinas para crear análisis integrados y de alto valor.", "Identifica patrones y relaciones no evidentes entre indicadores de negocio que revelan oportunidades estratégicas.", "Traduce conceptos teóricos en aplicaciones prácticas adaptadas al contexto específico del cliente."], "low_skill_behaviour": ["Aplica conceptos de management de forma mecánica, sin comprender realmente su significado o implicaciones.", "Confunde términos básicos de negocio o los utiliza incorrectamente en análisis y presentaciones.", "Evita preguntar cuando no comprende conceptos, prefiriendo asumir interpretaciones potencialmente incorrectas."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase inicial antes de ejecutar cualquier módulo o análisis. Se espera que el analista se familiarice con los conceptos básicos del proyecto, entienda el contexto del cliente y domine el vocabulario esencial para poder contribuir con sentido. Su foco está en adquirir conocimiento suficiente para trabajar con criterio, no solo seguir instrucciones."}, {"code": "151", "name": "Conocimiento del cliente y del sector", "description": "Permite al analista obtener y comprender información relevante del cliente y del sector para contextualizar y orientar el análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Revisa información general del cliente (qui<PERSON><PERSON> son, qué hacen, estructura básica).", "Lee documentación previa del proyecto (briefing, presentación inicial, press releases).", "Identifica los principales productos, mercados o líneas de negocio del cliente.", "Investiga fuentes públicas si necesita contexto (web corporativa, artículos, LinkedIn…)."], "talented_behaviour": ["Construye rápidamente un mapa mental del sector y la posición competitiva del cliente con información limitada.", "Identifica tendencias emergentes en el sector que podrían impactar al cliente, incluso cuando no están explícitamente mencionadas.", "Conecta información del cliente con experiencias previas relevantes para generar insights de valor añadido."], "low_skill_behaviour": ["Se limita a la información proporcionada sin buscar contexto adicional sobre el cliente o sector.", "Confunde aspectos básicos del negocio del cliente o malinterpreta su modelo operativo.", "Ignora información relevante sobre el cliente que podría impactar el enfoque o alcance del proyecto."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase inicial antes de ejecutar cualquier módulo o análisis. Se espera que el analista se familiarice con los conceptos básicos del proyecto, entienda el contexto del cliente y domine el vocabulario esencial para poder contribuir con sentido. Su foco está en adquirir conocimiento suficiente para trabajar con criterio, no solo seguir instrucciones."}, {"code": "152", "name": "Conocimiento del proyecto", "description": "Facilita la comprensión integral del proyecto al revisar documentos clave y alinear el trabajo del analista con los objetivos generales.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["<PERSON> los documentos clave del proyecto (propuesta, kick-off, cronograma, entregables esperados).", "Comprende el objetivo general del proyecto y cómo encaja su trabajo dentro de él.", "Pregunta qué temas ya han sido trabajados y qué aprendizajes previos existen.", "Anota definiciones clave para asegurar consistencia terminológica desde el principio."], "talented_behaviour": ["Identifica rápidamente las interdependencias entre diferentes componentes del proyecto y anticipa posibles cuellos de botella.", "Relaciona los objetivos del proyecto con las prioridades estratégicas más amplias del cliente.", "Detecta supuestos implícitos en la definición del proyecto que podrían requerir validación."], "low_skill_behaviour": ["Se enfoca exclusivamente en su tarea inmediata sin comprender cómo encaja en el contexto general del proyecto.", "Omite revisar documentación clave del proyecto, resultando en malentendidos sobre alcance u objetivos.", "Utiliza terminología inconsistente que genera confusión en comunicaciones internas y con el cliente."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase inicial antes de ejecutar cualquier módulo o análisis. Se espera que el analista se familiarice con los conceptos básicos del proyecto, entienda el contexto del cliente y domine el vocabulario esencial para poder contribuir con sentido. Su foco está en adquirir conocimiento suficiente para trabajar con criterio, no solo seguir instrucciones."}, {"code": "153", "name": "Comprensión del encargo", "description": "Asegura que el analista entiende completamente el requerimiento, reformulándolo y clarificando los límites y objetivos del análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Reformula el encargo en sus propias palabras para confirmar que ha entendido lo que se le pide.", "Pregunta por el objetivo del análisis y cómo se va a usar el resultado.", "Clarifica límites del análisis: qué entra y qué no, qué formato se espera y cuándo.", "Identifica definiciones clave, restricciones o supuestos relevantes antes de empezar."], "talented_behaviour": ["Anticipa necesidades implícitas del encargo que no han sido explícitamente mencionadas.", "Propone ajustes al alcance que podrían mejorar significativamente el valor del análisis.", "Identifica conexiones entre el encargo actual y otros análisis previos o paralelos que podrían enriquecer el resultado."], "low_skill_behaviour": ["Comienza a trabajar sin confirmar su comprensión del encargo, resultando en esfuerzos mal dirigidos.", "Asume aspectos críticos del análisis sin clarificarlos, generando resultados que no cumplen las expectativas.", "Ignora restricciones o limitaciones importantes que condicionan la validez del análisis."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier anális<PERSON>, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. En este punto, el analista no lidera el enfoque del módulo, pero sí debe entender perfectamente el encargo, descomponerlo en preguntas concretas y aportar estructura a partir de lo que define el consultor o manager."}, {"code": "154", "name": "Definición de preguntas clave", "description": "Divide el reto en preguntas específicas y accionables, facilitando un análisis enfocado y basado en datos.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Descompone el reto en preguntas más pequeñas y claras para poder atacarlas con datos.", "Clasifica las preguntas por tipo (qué pasa, por qué pasa, cómo evoluciona).", "Agrupa preguntas por eje (producto, canal, cliente…) para facilitar el análisis.", "Valida con el consultor que está interpretando correctamente las preguntas clave."], "talented_behaviour": ["Formula preguntas incisivas que van al núcleo del problema y revelan aspectos no considerados inicialmente.", "Crea marcos analíticos propios para organizar preguntas complejas en estructuras coherentes y manejables.", "Identifica relaciones causales entre diferentes preguntas que permiten abordar el análisis de forma más integrada."], "low_skill_behaviour": ["Plantea preguntas demasiado generales o ambiguas que no pueden responderse con datos concretos.", "Aborda el problema como un todo sin descomponerlo en elementos analizables.", "Mezcla preguntas de diferente naturaleza sin establecer una estructura lógica para el análisis."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier anális<PERSON>, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. En este punto, el analista no lidera el enfoque del módulo, pero sí debe entender perfectamente el encargo, descomponerlo en preguntas concretas y aportar estructura a partir de lo que define el consultor o manager."}, {"code": "155", "name": "Estructuración del problema e hipótesis", "description": "Establece una estructura lógica del problema y formula hipótesis para guiar y validar el proceso analítico.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Ayuda a construir una tabla de hipótesis a partir de las preguntas definidas.", "Propone una segmentación lógica para analizar el problema.", "Organiza visualmente la lógica del problema (esquema, slide o tabla estructurada).", "Distingue hipótesis ya validadas de las que requieren confirmación."], "talented_behaviour": ["Desarrolla hipótesis alternativas que desafían suposiciones convencionales y abren nuevas líneas de análisis.", "Crea estructuras analíticas elegantes que simplifican problemas complejos sin perder matices importantes.", "Anticipa posibles resultados contradictorios y diseña el análisis para poder reconciliarlos."], "low_skill_behaviour": ["Formula hipótesis vagas o no contrastables que no orientan efectivamente el análisis.", "Presenta la estructura del problema de forma desorganizada o confusa, dificultando su comprensión.", "Confunde hechos establecidos con hipótesis que requieren validación, comprometiendo la objetividad del análisis."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier anális<PERSON>, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. En este punto, el analista no lidera el enfoque del módulo, pero sí debe entender perfectamente el encargo, descomponerlo en preguntas concretas y aportar estructura a partir de lo que define el consultor o manager."}, {"code": "156", "name": "Definición del propósito del análisis", "description": "Clarifica y alinea el propósito del análisis con los objetivos del módulo, definiendo qué se espera lograr.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Reformula qué pregunta quiere responder el análisis asignado.", "Pregunta qué tipo de output se espera (gráfico, tabla, ratio).", "Asegura que entiende cómo ese análisis aporta al módulo o a la hipótesis.", "Identifica si hay partes poco claras del encargo y lo valida con el consultor."], "talented_behaviour": ["Conecta rápidamente el objetivo del análisis con la estrategia general del proyecto, anticipando cómo se utilizarán los resultados.", "Propone enfoques alternativos que podrían responder mejor a la pregunta planteada.", "Identifica oportunidades para ampliar el valor del análisis sin desviarse del objetivo principal."], "low_skill_behaviour": ["Acepta encargos sin comprender su propósito o cómo se integran en el contexto más amplio.", "Interpreta erróneamente el objetivo del análisis, resultando en outputs que no responden a la necesidad real.", "Ignora señales de que su comprensión del encargo es incompleta o incorrecta."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que se planifica cómo se va a responder a cada pregunta clave. El analista no define el enfoque general, pero sí debe traducir un encargo concreto en un análisis bien planteado: qué se va a calcular, con qué datos y en qué estructura."}, {"code": "157", "name": "Diseño técnico del análisis", "description": "Planifica y organiza los aspectos técnicos del análisis, estableciendo la estructura, variables y métodos necesarios.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Enumera variables y filtros necesarios (segmentos, periodos, unidades).", "Propone qué estructura usar (pestañas, tablas, secciones de Excel).", "Identifica fuentes de datos disponibles o a solicitar.", "Detecta posibles dificultades técnicas antes de empezar (datos faltantes, cálculos complejos)."], "talented_behaviour": ["Diseña estructuras analíticas elegantes que facilitan tanto el procesamiento como la interpretación de datos complejos.", "Anticipa necesidades de análisis secundarios y prepara la estructura para acomodarlos eficientemente.", "Optimiza el diseño técnico para equilibrar profundidad analítica con claridad y usabilidad."], "low_skill_behaviour": ["Crea estructuras de análisis desorganizadas o excesivamente complicadas que dificultan el trabajo posterior.", "Omite variables o filtros clave necesarios para responder adecuadamente a la pregunta planteada.", "Subestima la complejidad técnica del análisis, resultando en retrasos o resultados incompletos."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que se planifica cómo se va a responder a cada pregunta clave. El analista no define el enfoque general, pero sí debe traducir un encargo concreto en un análisis bien planteado: qué se va a calcular, con qué datos y en qué estructura."}, {"code": "158", "name": "Validación del diseño analítico", "description": "Fomenta la coherencia del equipo asegurando que el diseño del análisis sea compartido y validado entre los involucrados.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Comparte un borrador del enfoque (estructura de archivo o ejemplo simple).", "Valida con el consultor que la lógica del diseño es la correcta.", "Toma notas estructuradas sobre supuestos o criterios dados por el consultor.", "Ajusta el diseño antes de cargar datos reales para evitar rehacer trabajo."], "talented_behaviour": ["Presenta múltiples opciones de diseño con sus respectivas ventajas y limitaciones para facilitar la toma de decisiones.", "Incorpora rápidamente el feedback recibido, mejorando significativamente la propuesta inicial.", "Documenta claramente los supuestos y decisiones de diseño para asegurar consistencia a lo largo del proyecto."], "low_skill_behaviour": ["Evita validar su enfoque con el equipo hasta tener un producto terminado, resultando en retrabajos significativos.", "Muestra resistencia a incorporar feedback que implique modificar su diseño original.", "Comunica de forma confusa o incompleta los supuestos utilizados en el diseño del análisis."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que se planifica cómo se va a responder a cada pregunta clave. El analista no define el enfoque general, pero sí debe traducir un encargo concreto en un análisis bien planteado: qué se va a calcular, con qué datos y en qué estructura."}, {"code": "159", "name": "Búsqueda y recopilación de datos", "description": "Realiza una exhaustiva búsqueda y recopilación de datos, identificando tanto la información existente como las necesidades adicionales.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Revisa si los datos existen ya o deben pedirse al cliente o buscarse online.", "Solicita datos de forma clara y completa, si le asignan esa tarea.", "Extrae la información necesaria de los documentos compartidos.", "Anota la fuente de cada dato y el formato original recibido."], "talented_behaviour": ["Identifica fuentes alternativas de datos cuando las principales no están disponibles o son insuficientes.", "Anticipa necesidades de datos adicionales que podrían enriquecer el análisis y los solicita proactivamente.", "Organiza eficientemente la recopilación de datos para minimizar iteraciones con el cliente."], "low_skill_behaviour": ["Solicita datos de forma ambigua o incompleta, generando múltiples iteraciones innecesarias.", "Se limita estrictamente a los datos solicitados sin cuestionar si son suficientes para el análisis requerido.", "<PERSON><PERSON> la trazabilidad de las fuentes de datos, comprometiendo la credibilidad del análisis."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase de recopilación, limpieza y preparación de datos para que el análisis pueda ejecutarse correctamente. El analista es responsable de dejar listas las bases de datos con orden, trazabilidad y calidad suficiente."}, {"code": "160", "name": "Preparación y limpieza de datos", "description": "Garantiza que los datos estén listos para el análisis mediante la limpieza, normalización y organización adecuada de la información.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Crea una copia limpia y editable de la base de datos recibida.", "Normaliza formatos: fechas, monedas, nombres de categorías.", "Elimina duplicados y rellena valores cuando sea posible.", "Aplica filtros y cortes según el diseño definido con el consultor."], "talented_behaviour": ["Desarrolla procesos eficientes de limpieza que pueden reutilizarse en actualizaciones futuras de datos.", "Identifica y corrige patrones sistemáticos de errores en los datos que otros podrían pasar por alto.", "Implementa transformaciones creativas que convierten datos problemáticos en información útil para el análisis."], "low_skill_behaviour": ["Realiza cambios en los datos originales sin documentación o respaldo, comprometiendo la integridad del análisis.", "Aplica correcciones inconsistentes a problemas similares en diferentes partes del conjunto de datos.", "Ignora anomalías evidentes en los datos que podrían afectar significativamente los resultados."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase de recopilación, limpieza y preparación de datos para que el análisis pueda ejecutarse correctamente. El analista es responsable de dejar listas las bases de datos con orden, trazabilidad y calidad suficiente."}, {"code": "161", "name": "Revisión de calidad de datos", "description": "Verifica la integridad y consistencia de los datos mediante revisiones sistemáticas que detectan errores y anomalías.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Revisa consistencia básica: totales, promedios, gaps evidentes.", "Marca en rojo o anota cualquier dato dudoso o incompleto.", "Agrega comentarios explicativos si aplica cambios o estimaciones.", "Organiza el archivo con pestañas y colores que faciliten la lectura del consultor."], "talented_behaviour": ["Implementa verificaciones cruzadas sofisticadas para detectar inconsistencias sutiles en los datos.", "Desarrolla visualizaciones exploratorias que revelan patrones y anomalías en los datos.", "Establece estándares de calidad que elevan la precisión y confiabilidad de todo el análisis."], "low_skill_behaviour": ["Realiza revisiones superficiales que no detectan problemas significativos en los datos.", "Deja datos problemáticos sin marcar o documentar, creando confusión en etapas posteriores.", "Presenta datos sin una organización lógica, dificultando su revisión e interpretación."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase de recopilación, limpieza y preparación de datos para que el análisis pueda ejecutarse correctamente. El analista es responsable de dejar listas las bases de datos con orden, trazabilidad y calidad suficiente."}, {"code": "162", "name": "Comprensión de resultados", "description": "Interpreta los resultados del análisis para verificar que sean coherentes y relevantes en relación con la pregunta original.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Revisa que los outputs calculados sean lógicos y estén completos.", "Comprueba que los datos responden a la pregunta original del encargo.", "Detecta patrones relevantes: tendencias, caídas, diferencias clave.", "Señala resultados inesperados o inconsistentes para validarlos con el consultor."], "talented_behaviour": ["Identifica relaciones no evidentes entre diferentes variables que revelan insights de alto valor.", "Contextualiza los resultados dentro de marcos conceptuales relevantes que facilitan su interpretación.", "Distingue entre correlaciones casuales y patrones significativos con implicaciones estratégicas."], "low_skill_behaviour": ["Acepta resultados anómalos sin cuestionarlos o validarlos adecuadamente.", "Se enfoca en aspectos superficiales de los datos sin identificar patrones o tendencias relevantes.", "<PERSON>de de vista la pregunta original, generando análisis que no responden al objetivo planteado."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase donde el analista extrae aprendizajes clave de lo que dicen los datos, y aporta valor con lecturas estructuradas, no solo con cálculos. No es su rol dar recomendaciones estratégicas, pero sí debe ayudar a entender lo que muestran los números."}, {"code": "163", "name": "Evaluación de hipótesis", "description": "Relaciona los resultados obtenidos con las hipótesis iniciales, permitiendo identificar conclusiones y áreas de duda para profundizar.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Ordena los resultados por hipótesis o bloque temático.", "Anota conclusiones preliminares directamente en el archivo.", "Diferencia claramente datos, interpretación y duda.", "Pregunta si debe profundizar o complementar un análisis no concluyente."], "talented_behaviour": ["Conecta resultados de diferentes análisis para construir una narrativa coherente sobre las hipótesis.", "Identifica implicaciones de segundo orden que no son evidentes en los resultados directos.", "Propone análisis complementarios específicos cuando los resultados son ambiguos o contradictorios."], "low_skill_behaviour": ["Mezcla hechos objetivos con interpretaciones personales sin diferenciarlos claramente.", "Fuerza conclusiones que no están respaldadas por los datos para confirmar hipótesis preestablecidas.", "Evita señalar cuando los resultados son inconcluyentes o contradicen las expectativas iniciales."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase donde el analista extrae aprendizajes clave de lo que dicen los datos, y aporta valor con lecturas estructuradas, no solo con cálculos. No es su rol dar recomendaciones estratégicas, pero sí debe ayudar a entender lo que muestran los números."}, {"code": "164", "name": "Identificación de insights clave", "description": "Extrae y sintetiza las ideas clave del análisis para preparar mensajes claros que orienten la toma de decisiones.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Extrae 2–3 ideas clave por análisis, redactadas de forma clara y directa.", "Propone frases de insight que podrían usarse en una slide.", "Señala qué tablas o gráficos aportan más valor.", "Elimina o marca como secundarios los análisis poco útiles."], "talented_behaviour": ["Formula mensajes que capturan perfectamente la esencia de hallazgos complejos de forma accesible y memorable.", "Jerar<PERSON>za insights con precisión, destacando a<PERSON> con mayor impacto estratégico para el cliente.", "Traduce datos técnicos en narrativas convincentes que conectan con las preocupaciones del negocio."], "low_skill_behaviour": ["Presenta demasiadas ideas sin priorizar, diluyendo el impacto de los hallazgos realmente importantes.", "Formula mensajes ambiguos o excesivamente técnicos que no comunican claramente el valor del análisis.", "<PERSON><PERSON><PERSON> an<PERSON> de bajo valor que distraen de los mensajes principales."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase donde el analista extrae aprendizajes clave de lo que dicen los datos, y aporta valor con lecturas estructuradas, no solo con cálculos. No es su rol dar recomendaciones estratégicas, pero sí debe ayudar a entender lo que muestran los números."}, {"code": "165", "name": "Traducción del análisis en mensaje", "description": "Convierte los hallazgos del análisis en un mensaje conciso y estructurado para su presentación en formato slide.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Redacta un título de slide con mensaje, no con etiqueta (“Las ventas caen un 12%”, no “Ventas”).", "Sintetiza el insight principal en 1–2 bullets.", "Relaciona el mensaje con la hipótesis o pregunta a la que responde.", "Verifica que la slide no dé pie a dobles interpretaciones."], "talented_behaviour": ["Crea títulos impactantes que capturan la atención y transmiten inmediatamente el mensaje clave.", "Estructura la información con una lógica narrativa que guia naturalmente hacia la conclusión principal.", "Anticipa y responde preguntas implícitas que podrían surgir al presentar el análisis."], "low_skill_behaviour": ["Utiliza títulos descriptivos sin mensaje claro que obligan al lector a interpretar los datos.", "Presenta información desconectada de la hipótesis o pregunta original que se pretendía responder.", "Incluye demasiados detalles o mensajes secundarios que diluyen el punto principal."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el analista convierte su análisis en materiales visuales claros, que el consultor puede integrar fácilmente en el storyline. No presenta al cliente, pero sí debe aportar slides bien estructuradas, entendibles y pulidas."}, {"code": "166", "name": "Construcción de la slide", "description": "Diseña y construye slides visualmente coherentes que facilitan la comunicación clara de los hallazgos analíticos.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Usa formatos limpios, consistentes y alineados con el equipo (colores, fuentes, logos).", "Prepara gráficos y tablas con foco en la lectura, no en la decoración.", "Alinea bien elementos visuales y respeta jerarquía de información.", "Revisa ortografía, cifras clave y consistencia visual antes de entregar."], "talented_behaviour": ["Crea visualizaciones innovadoras que comunican información compleja de forma inmediatamente comprensible.", "Adapta el diseño visual para destacar exactamente los puntos más relevantes sin distracciones.", "Mantiene coherencia visual perfecta incluso en presentaciones extensas con múltiples contribuyentes."], "low_skill_behaviour": ["Sobrecarga las slides con demasiados elementos visuales o texto, dificultando la comprensión del mensaje.", "Utiliza formatos inconsistentes que generan una impresión de desorden y falta de profesionalidad.", "Prioriza elementos decorativos sobre la claridad y legibilidad de la información."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el analista convierte su análisis en materiales visuales claros, que el consultor puede integrar fácilmente en el storyline. No presenta al cliente, pero sí debe aportar slides bien estructuradas, entendibles y pulidas."}, {"code": "167", "name": "Apoyo a la preparación de entregables", "description": "Facilita la integración de los entregables en una presentación coherente, asegurando consistencia y claridad en el mensaje final.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "<PERSON><PERSON><PERSON>", "expert_behaviour": ["Ordena sus slides dentro del documento de módulo o storyline.", "Sugiere dónde ubicar la slide si tiene claridad sobre el flujo.", "Integra sus entregables con el resto del equipo sin solapar contenido.", "Ajusta rápidamente si recibe feedback de consultor o manager."], "talented_behaviour": ["Anticipa cómo sus entregables encajarán en la narrativa general, facilitando la integración sin fricciones.", "Propone mejoras estructurales al storyline que elevan la claridad y el impacto de todo el documento.", "Incorpora feedback con rapidez excepcional, transformando críticas en mejoras sustanciales."], "low_skill_behaviour": ["Entrega slides aisladas sin considerar cómo se integran en el contexto más amplio del documento.", "Muestra resistencia a modificar su trabajo cuando recibe feedback constructivo.", "Ignora las convenciones establecidas por el equipo, creando inconsistencias en el entregable final."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el analista convierte su análisis en materiales visuales claros, que el consultor puede integrar fácilmente en el storyline. No presenta al cliente, pero sí debe aportar slides bien estructuradas, entendibles y pulidas."}, {"code": "168", "name": "Conocimientos de management", "description": "Aplica su amplio conocimiento en management para interpretar indicadores financieros y traducir datos en insights estratégicos.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Identifica qué conceptos financieros y de negocio son clave para el proyecto y el cliente (ej. margen por canal, coste de adquisición, lifetime value…).", "Interpreta indicadores y relaciones de forma integrada (por ejemplo, cómo afecta una bajada de margen a la rentabilidad del segmento).", "Utiliza marcos conceptuales o financieros para orientar el análisis o la recomendación.", "Traduce datos de negocio en insights económicos relevantes para la toma de decisiones."], "talented_behaviour": ["Desarrolla modelos financieros sofisticados que revelan dinámicas no evidentes del negocio del cliente.", "Conecta conceptos de diferentes disciplinas para crear marcos analíticos innovadores y altamente efectivos.", "Anticipa implicaciones financieras de segundo y tercer orden en decisiones estratégicas complejas."], "low_skill_behaviour": ["Aplica conceptos financieros de forma mecánica sin comprender realmente su relevancia para el contexto específico.", "Analiza indicadores de forma aislada, perdiendo relaciones importantes entre diferentes métricas.", "Presenta datos financieros sin traducirlos en implicaciones prácticas para la toma de decisiones."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase de preparación para asegurar que el consultor entra al proyecto con criterio estratégico, conocimiento contextual y visión de conjunto. Se espera que sea capaz de interpretar el reto del cliente, conectar aprendizajes previos y aplicar marcos de negocio relevantes desde el inicio."}, {"code": "169", "name": "Conocimiento del cliente y del sector", "description": "Utiliza información detallada del cliente y del sector para orientar el análisis con una visión estratégica y contextualizada.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Estudia la historia, estructura y drivers clave del cliente para entender su lógica interna.", "Identifica principales dinámicas del sector (tendencias, players, barreras, regulación…).", "Revisa benchmark o aprendizajes de proyectos similares, internos o externos.", "Detecta oportunidades o riesgos relevantes para el cliente que puedan no estar en el brief."], "talented_behaviour": ["Desarrolla rápidamente un conocimiento profundo del sector que rivaliza con el de profesionales con años de experiencia.", "Identifica patrones y tendencias emergentes que aún no son evidentes para la mayoría de los competidores.", "Conecta información del cliente con tendencias macro para anticipar cambios disruptivos en el mercado."], "low_skill_behaviour": ["Mantiene un conocimiento superficial del cliente y su sector, limitando la profundidad y relevancia del análisis.", "Ignora el contexto histórico y competitivo que explica la situación actual del cliente.", "Aplica soluciones genéricas sin adaptarlas a las particularidades del cliente y su entorno."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase de preparación para asegurar que el consultor entra al proyecto con criterio estratégico, conocimiento contextual y visión de conjunto. Se espera que sea capaz de interpretar el reto del cliente, conectar aprendizajes previos y aplicar marcos de negocio relevantes desde el inicio."}, {"code": "170", "name": "Comprensión estratégica del proyecto", "description": "Revisa detalladamente la propuesta y los entregables para comprender la estrategia y los objetivos del proyecto desde una perspectiva global.", "categoria": "competencias_tecnicas", "factor": "Bloque 0: Preparar el Engagement", "group": "Preparar el Engagement", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Revisa con atención la propuesta, el enfoque metodológico y los entregables comprometidos.", "Entiende el posicionamiento estratégico del proyecto dentro del cliente.", "Identifica stakeholders clave y dinámicas políticas que pueden influir en el éxito del trabajo.", "Define desde el inicio cómo se medirá el impacto o éxito del proyecto."], "talented_behaviour": ["Identifica rápidamente oportunidades para ampliar el valor del proyecto más allá del alcance inicial.", "Anticipa obstáculos potenciales en la implementación y desarrolla estrategias para superarlos.", "Conecta objetivos del proyecto con iniciativas estratégicas más amplias del cliente, maximizando su impacto."], "low_skill_behaviour": ["Se enfoca exclusivamente en aspectos técnicos sin comprender el contexto estratégico del proyecto.", "Ignora las dinámicas organizacionales y políticas que pueden afectar la implementación de recomendaciones.", "Trabaja sin métricas claras de éxito, dificultando la evaluación del impacto real del proyecto."], "introducción_al_factor": "¿Qué es exactamente Preparing the Engagement?\nEs la fase de preparación para asegurar que el consultor entra al proyecto con criterio estratégico, conocimiento contextual y visión de conjunto. Se espera que sea capaz de interpretar el reto del cliente, conectar aprendizajes previos y aplicar marcos de negocio relevantes desde el inicio."}, {"code": "171", "name": "Aterrizaje del reto en un módulo", "description": "Traduce el reto global del cliente en un módulo concreto y accionable, definiendo el alcance y la profundidad necesarios.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Reformula el reto del cliente en una pregunta clara, accionable y conectada con el foco del proyecto.", "Define el alcance del módulo: qué entra, qué no, y con qué profundidad debe abordarse.", "Aclara con el manager qué decisión debe habilitar el módulo o qué hipótesis debe validar.", "Identifica dependencias clave con otros módulos del proyecto."], "talented_behaviour": ["Reformula problemas complejos en preguntas precisas que revelan oportunidades de alto impacto no evidentes inicialmente.", "Diseña módulos con límites claros pero flexibles que permiten adaptarse a descubrimientos inesperados.", "Anticipa cómo los resultados del módulo influirán en otras áreas del proyecto, optimizando las interdependencias."], "low_skill_behaviour": ["Define el alcance de forma ambigua o demasiado amplia, dificultando la ejecución efectiva del módulo.", "Pierde de vista la conexión entre el módulo y las decisiones estratégicas que debe informar.", "Trabaja de forma aislada sin considerar las dependencias con otros componentes del proyecto."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier análisis, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. El consultor lidera el framing de su módulo: aterriza el reto, define las hipótesis y asegura que el análisis estará orientado a decisiones relevantes para el cliente."}, {"code": "172", "name": "Estructuración del problema", "description": "Descompone y organiza el problema en hipótesis contrastables, estableciendo un marco claro para el análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Descompone el problema en hipótesis contrastables, orientadas a decisiones concretas.", "Prioriza las hipótesis según impacto y nivel de incertidumbre.", "Explicita el racional detrás de cada hipótesis y sus implicaciones potenciales.", "Prepara un esquema visual (issue tree, mapa de hipótesis) para organizar la lógica del módulo."], "talented_behaviour": ["Desarrolla marcos analíticos innovadores que revelan dimensiones no evidentes del problema.", "Identifica hipótesis contraintuitivas pero potencialmente transformadoras que otros pasan por alto.", "Crea estructuras visuales elegantes que simplifican problemas extremadamente complejos sin perder matices importantes."], "low_skill_behaviour": ["Formula hipótesis vagas o no verificables que no orientan efectivamente el análisis.", "Trata todas las hipótesis con igual prioridad, desperdiciando recursos en áreas de bajo impacto.", "Presenta la estructura del problema de forma desorganizada, dificultando la comprensión por parte del equipo."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier análisis, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. El consultor lidera el framing de su módulo: aterriza el reto, define las hipótesis y asegura que el análisis estará orientado a decisiones relevantes para el cliente."}, {"code": "173", "name": "Planificación del análisis", "description": "Define y orienta el enfoque analítico, identificando métodos adecuados y anticipando posibles limitaciones en el proceso.", "categoria": "competencias_tecnicas", "factor": "Bloque 1: <PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON> el Problema", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Determina qué tipo de análisis permitiría validar cada hipótesis.", "Identifica gaps de información y anticipa posibles limitaciones.", "Traduce el enfoque del módulo en encargos concretos y entendibles para los analistas.", "Valida con el manager que la lógica del módulo está bien enfocada antes de avanzar."], "talented_behaviour": ["Diseña enfoques analíticos innovadores que resuelven limitaciones aparentemente insuperables.", "Anticipa con precisión qué información será más valiosa, optimizando la eficiencia del proceso de recolección.", "Traduce conceptos complejos en instrucciones claras que potencian la productividad y creatividad del equipo."], "low_skill_behaviour": ["Selecciona métodos analíticos inadecuados o insuficientes para validar las hipótesis planteadas.", "Subestima las limitaciones de datos o tiempo, generando expectativas poco realistas.", "Comunica instrucciones ambiguas o excesivamente complejas que confunden al equipo analítico."], "introducción_al_factor": "¿Qué es exactamente Framing the Problem?\nEs el paso previo a cualquier análisis, en el que se define con claridad qué se quiere resolver y cómo se va a enfocar el problema. El consultor lidera el framing de su módulo: aterriza el reto, define las hipótesis y asegura que el análisis estará orientado a decisiones relevantes para el cliente."}, {"code": "174", "name": "Estructuración de análisis por hipótesis", "description": "Convierte las hipótesis definidas en un plan analítico estructurado y secuencial, vinculando cada análisis con una decisión clave.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Selecciona el tipo de análisis más adecuado para validar cada hipótesis.", "Ordena los análisis de forma lógica y progresiva, desde diagnóstico hasta implicación.", "Determina el nivel de profundidad necesario para cada análisis.", "Asegura que cada análisis esté vinculado a una decisión o mensaje clave."], "talented_behaviour": ["Diseña secuencias analíticas elegantes que maximizan el aprendizaje mientras minimizan el esfuerzo requerido.", "Identifica análisis críticos que pueden desbloquear múltiples hipótesis simultáneamente.", "Anticipa cómo diferentes resultados analíticos podrían combinarse para generar insights transformadores."], "low_skill_behaviour": ["Propone análisis desconectados de las hipótesis o que no conducen a conclusiones accionables.", "Organiza análisis de forma aleatoria o ilógica, dificultando la construcción de una narrativa coherente.", "Dedica recursos excesivos a análisis de bajo impacto mientras descuida áreas críticas para la toma de decisiones."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que el consultor convierte las hipótesis en un plan de análisis estructurado, útil y accionable. Define qué análisis se harán, cómo deben ejecutarse y da dirección a los analistas para asegurar foco, consistencia y calidad."}, {"code": "175", "name": "Estructuración técnica del análisis", "description": "Establece el marco estructural del análisis definiendo variables, filtros y formatos para garantizar resultados claros y accionables.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Define variables, filtros, cortes y métricas clave a incluir en el análisis.", "Indica el formato esperado del output (gráfico, tabla, ratio, slide…).", "Documenta supuestos relevantes que condicionen los resultados.", "Anticipa posibles retos técnicos o de datos y plantea alternativas."], "talented_behaviour": ["Diseña estructuras analíticas innovadoras que revelan patrones ocultos en datos complejos.", "Identifica métricas no convencionales que proporcionan perspectivas únicas sobre el problema.", "Desarrolla soluciones creativas para superar limitaciones aparentemente insalvables en los datos disponibles."], "low_skill_behaviour": ["Define variables o métricas ambiguas que generan confusión en el equipo analítico.", "Omite documentar supuestos críticos, comprometiendo la validez e interpretación de los resultados.", "Ignora restricciones técnicas evidentes, resultando en análisis inviables o significativamente retrasados."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que el consultor convierte las hipótesis en un plan de análisis estructurado, útil y accionable. Define qué análisis se harán, cómo deben ejecutarse y da dirección a los analistas para asegurar foco, consistencia y calidad."}, {"code": "176", "name": "Dirección del equipo analítico", "description": "Lidera y coordina el equipo analítico, asegurando que todos comprendan los objetivos y ejecuten el análisis con precisión.", "categoria": "competencias_tecnicas", "factor": "Bloque 2: Diseñar el Analisis", "group": "Diseñar el Analisis", "source": "Signature", "role": "Consultor", "expert_behaviour": ["<PERSON><PERSON>a tareas claras y bien delimitadas a los analistas.", "Asegura que todos entienden el objetivo, el enfoque y el criterio del análisis.", "Revisa las maquetas o estructuras propuestas por los analistas antes de cargar datos.", "Corrige desvíos metodológicos o de foco con feedback rápido y concreto."], "talented_behaviour": ["<PERSON><PERSON>a tareas que maximizan tanto la contribución al proyecto como el desarrollo profesional de cada analista.", "Comunica el contexto estratégico de forma que inspira y empodera al equipo para innovar dentro de parámetros claros.", "Proporciona feedback constructivo que transforma significativamente la calidad del trabajo sin desmoralizar."], "low_skill_behaviour": ["<PERSON><PERSON>a tareas ambiguas o excesivamente amplias que generan confusión y trabajo ineficiente.", "Omite explicar el propósito estratégico, limitando la capacidad del equipo para aportar valor adicional.", "Proporciona feedback tardío o poco específico que no permite corregir el rumbo efectivamente."], "introducción_al_factor": "¿Qué es exactamente Designing the Analysis?\nEs la fase en la que el consultor convierte las hipótesis en un plan de análisis estructurado, útil y accionable. Define qué análisis se harán, cómo deben ejecutarse y da dirección a los analistas para asegurar foco, consistencia y calidad."}, {"code": "177", "name": "Identificación de requerimientos de datos", "description": "Determina y prioriza los requerimientos de datos esenciales para validar las hipótesis y ejecutar el análisis de manera eficaz.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Define qué datos específicos se requieren para cada análisis, con claridad de variables, cortes y periodo.", "Verifica que la cobertura de los datos permite responder a las hipótesis planteadas.", "Diferencia qué datos ya existen y cuáles deben solicitarse al cliente o buscarse externamente.", "Prioriza las peticiones de datos para evitar cuellos de botella y esperas innecesarias."], "talented_behaviour": ["Identifica fuentes de datos no convencionales que proporcionan ventajas competitivas en el análisis.", "Anticipa con precisión qué datos serán más valiosos, optimizando significativamente el proceso de recolección.", "Diseña estrategias de recolección de datos que minimizan la carga para el cliente mientras maximizan el valor analítico."], "low_skill_behaviour": ["Solicita datos excesivos o irrelevantes que sobrecargan al cliente sin aportar valor al análisis.", "Omite verificar si los datos solicitados realmente permitirán validar las hipótesis planteadas.", "Gestiona las peticiones de datos de forma desorganizada, generando retrasos y frustración en el equipo y el cliente."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase en la que se recopilan, preparan y validan los datos necesarios para ejecutar el análisis. El consultor no extrae ni limpia todos los datos, pero se asegura de que el equipo accede a la información correcta, que los datos cubren bien las hipótesis, y que se aplican los criterios adecuados en su tratamiento."}, {"code": "178", "name": "Supervisión de la recolección y preparación de datos", "description": "Supervisa y valida el proceso de obtención y transformación de datos para garantizar que la información cumple con los criterios requeridos.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Revisa que las peticiones de datos enviadas al cliente sean claras, completas y bien enfocadas.", "Da feedback a los analistas sobre cómo organizar las bases según el diseño del análisis.", "Valida que los filtros y transformaciones aplicados no distorsionen el sentido del dato.", "Revisa estructuras de Excel (inputs, cálculos, outputs) para asegurar lógica y orden."], "talented_behaviour": ["Diseña procesos de preparación de datos que optimizan tanto la calidad como la eficiencia del análisis posterior.", "Identifica oportunidades para automatizar tareas repetitivas de procesamiento de datos, liberando tiempo para análisis de mayor valor.", "Proporciona orientación técnica que eleva significativamente las capacidades del equipo en manejo de datos complejos."], "low_skill_behaviour": ["Permite que se envíen solicitudes de datos ambiguas o incompletas que generan múltiples iteraciones con el cliente.", "Ignora problemas estructurales en la organización de datos que comprometen la eficiencia del análisis posterior.", "Aplica transformaciones inconsistentes o incorrectas que distorsionan los resultados sin documentar los cambios."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase en la que se recopilan, preparan y validan los datos necesarios para ejecutar el análisis. El consultor no extrae ni limpia todos los datos, pero se asegura de que el equipo accede a la información correcta, que los datos cubren bien las hipótesis, y que se aplican los criterios adecuados en su tratamiento."}, {"code": "179", "name": "Control de calidad de datos", "description": "Realiza un riguroso control de calidad de los datos, asegurando que estos sean completos, consistentes y confiables para el análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 3: <PERSON><PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON><PERSON><PERSON>", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Comprueba que los datos recopilados cubren el alcance definido (segmentos, fechas, unidades, etc.).", "Revisa cifras clave, busca inconsistencias o outliers, y discute con el equipo si requieren revisión.", "Asegura trazabilidad de fuentes y criterios aplicados a los datos.", "Eleva al manager cualquier riesgo crítico de calidad o disponibilidad de datos que impacte el análisis."], "talented_behaviour": ["Implementa procesos de validación sistemáticos que detectan sutiles problemas de calidad que otros pasarían por alto.", "Desarrolla soluciones creativas para compensar limitaciones en los datos sin comprometer la integridad del análisis.", "Establece estándares de documentación que aseguran perfecta trazabilidad incluso en proyectos de alta complejidad."], "low_skill_behaviour": ["Realiza validaciones superficiales que no detectan problemas significativos en la calidad o cobertura de los datos.", "Acepta datos problemáticos sin cuestionar su validez o buscar alternativas para mejorar su calidad.", "Omite documentar adecuadamente las fuentes y transformaciones, comprometiendo la credibilidad y auditabilidad del análisis."], "introducción_al_factor": "¿Qué es exactamente Gathering the Data?\nEs la fase en la que se recopilan, preparan y validan los datos necesarios para ejecutar el análisis. El consultor no extrae ni limpia todos los datos, pero se asegura de que el equipo accede a la información correcta, que los datos cubren bien las hipótesis, y que se aplican los criterios adecuados en su tratamiento."}, {"code": "180", "name": "Revisión y validación de resultados", "description": "Verifica que los resultados del análisis sean coherentes y completos, y que se ajusten a los objetivos planteados en el proyecto.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Comprueba que los resultados obtenidos son lógicos, completos y consistentes con el diseño del análisis.", "Revisa los principales indicadores, ratios y segmentaciones para detectar patrones relevantes.", "Identifica errores, gaps o incoherencias que deben corregirse antes de extraer conclusiones.", "Asegura que los resultados están alineados con el scope del módulo y no se desvían de las hipótesis."], "talented_behaviour": ["Detecta patrones sutiles en los datos que revelan insights no evidentes pero altamente valiosos para el cliente.", "Identifica rápidamente anomalías en los resultados que otros pasarían por alto, asegurando máxima precisión.", "Mantiene una visión holística que conecta resultados de diferentes análisis en una narrativa coherente y reveladora."], "low_skill_behaviour": ["Acepta resultados sin verificar su coherencia lógica o consistencia con el diseño original del análisis.", "Pasa por alto patrones importantes o anomalías que podrían indicar errores o insights relevantes.", "Permite que el análisis se desvíe del scope definido, diluyendo su relevancia para las hipótesis planteadas."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase en la que el consultor lee, interpreta y extrae conclusiones relevantes de los análisis realizados por el equipo. No se limita a validar los resultados técnicos, sino que identifica qué significan, qué hipótesis confirman o descartan, y qué implicaciones tienen para el cliente y la recomendación."}, {"code": "181", "name": "Análisis de hipótesis e insights", "description": "Analiza en profundidad los resultados para determinar el grado de validación de las hipótesis y extraer insights que orienten la toma de decisiones.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Evalúa si los resultados confirman, matizan o refutan cada hipótesis planteada.", "Extrae los principales insights de cada análisis y prioriza los que aportan mayor valor.", "Identifica drivers clave de<PERSON><PERSON> de los datos (por qué ocurre lo que ocurre).", "Detecta interrelaciones entre variables que puedan explicar fenómenos relevantes."], "talented_behaviour": ["Sintetiza resultados complejos en insights transformadores que cambian fundamentalmente la comprensión del problema.", "Identifica relaciones causales no evidentes que explican fenómenos complejos de forma elegante y convincente.", "Conecta hallazgos de diferentes análisis para construir una narrativa integrada con implicaciones estratégicas profundas."], "low_skill_behaviour": ["Extrae conclusiones superficiales que no profundizan en las causas subyacentes de los fenómenos observados.", "Fuerza interpretaciones que confirman hipótesis preestablecidas, ignorando evidencia contradictoria.", "Trata cada análisis de forma aislada, perdiendo conexiones importantes entre diferentes resultados."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase en la que el consultor lee, interpreta y extrae conclusiones relevantes de los análisis realizados por el equipo. No se limita a validar los resultados técnicos, sino que identifica qué significan, qué hipótesis confirman o descartan, y qué implicaciones tienen para el cliente y la recomendación."}, {"code": "182", "name": "Integración en la narrativa y recomendación", "description": "Integra los insights obtenidos en una narrativa coherente que conecta los hallazgos del análisis con recomendaciones prácticas para el cliente.", "categoria": "competencias_tecnicas", "factor": "Bloque 4: Interpretar los Resultados", "group": "Interpretar los Resultados", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Traduce los insights en mensajes claros, accionables y orientados a decisiones.", "Propone cómo los resultados encajan en la narrativa general del proyecto.", "Aporta implicaciones prácticas: “qué debería hacer el cliente” a partir de los hallazgos.", "Identifica qué hipótesis o temas requieren un análisis complementario o más profundo."], "talented_behaviour": ["Formula recomendaciones estratégicas que transforman insights analíticos en ventajas competitivas tangibles para el cliente.", "Anticipa objeciones o resistencias potenciales a las conclusiones y desarrolla argumentos convincentes para superarlas.", "Identifica oportunidades de alto valor que emergen de los hallazgos pero que van más allá del alcance inicial del proyecto."], "low_skill_behaviour": ["Presenta hallazgos sin traducirlos en implicaciones prácticas o recomendaciones accionables.", "Propone conclusiones desconectadas de la narrativa general o de las prioridades estratégicas del cliente.", "Evita reconocer cuando los resultados son inconcluyentes o requieren análisis adicional."], "introducción_al_factor": "¿Qué es exactamente Interpreting the Results?\nEs la fase en la que el consultor lee, interpreta y extrae conclusiones relevantes de los análisis realizados por el equipo. No se limita a validar los resultados técnicos, sino que identifica qué significan, qué hipótesis confirman o descartan, y qué implicaciones tienen para el cliente y la recomendación."}, {"code": "183", "name": "Estructuración del mensaje", "description": "Organiza y sintetiza la información analítica en un mensaje final claro y estructurado que respalde la toma de decisiones.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Define el mensaje clave de cada análisis y cómo contribuye a la narrativa del módulo.", "Construye una lógica clara para el storyline: situación → análisis → implicación → recomendación.", "Decide qué análisis deben presentarse y cuáles pueden quedar como backup.", "Prioriza los mensajes por impacto, novedad o capacidad de generar decisión."], "talented_behaviour": ["Construye narrativas convincentes que transforman datos complejos en historias memorables con clara llamada a la acción.", "Estructura presentaciones con una lógica impecable que anticipa y responde a las preocupaciones clave de los stakeholders.", "Identifica el equilibrio perfecto entre rigor analítico y simplicidad comunicativa para maximizar el impacto."], "low_skill_behaviour": ["Presenta análisis sin una estructura lógica clara, dificultando la comprensión de la narrativa general.", "Incluye demasiados detalles o análisis secundarios que diluyen los mensajes principales.", "Organiza contenido sin considerar el impacto o relevancia para la toma de decisiones del cliente."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el consultor convierte los resultados del análisis en mensajes estructurados y comprensibles, conectados con la lógica del proyecto y enfocados a decisiones. Aunque no siempre lidera la presentación final, sí tiene un rol clave en estructurar el storyline, construir slides con sentido y elevar implicaciones al cliente."}, {"code": "184", "name": "Elaboración de materiales de presentación", "description": "Elabora materiales de presentación que integren visualmente y de forma coherente los insights y recomendaciones derivados del análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Redacta títulos con mensaje claro y accionable, no meramente descriptivo.", "Revisa y ajusta las slides preparadas por analistas para asegurar foco, claridad y jerarquía visual.", "Se asegura de que cada slide sea autocontenida y que el documento fluya con lógica.", "Valida que los outputs visuales (gráficos, tablas) transmitan correctamente el insight deseado."], "talented_behaviour": ["Crea materiales visualmente impactantes que comunican mensajes complejos con extraordinaria claridad y elegancia.", "Transforma slides estándar en piezas de comunicación altamente efectivas con ajustes precisos y creativos.", "Desarrolla visualizaciones innovadoras que revelan patrones y relaciones no evidentes en formatos convencionales."], "low_skill_behaviour": ["Produce slides sobrecargadas de información o con jerarquía visual confusa que dificulta la comprensión.", "Utiliza títulos genéricos o descriptivos que no transmiten el mensaje clave de la slide.", "Permite inconsistencias visuales o lógicas entre diferentes partes de la presentación."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el consultor convierte los resultados del análisis en mensajes estructurados y comprensibles, conectados con la lógica del proyecto y enfocados a decisiones. Aunque no siempre lidera la presentación final, sí tiene un rol clave en estructurar el storyline, construir slides con sentido y elevar implicaciones al cliente."}, {"code": "185", "name": "Desarrollo de comunicación", "description": "Prepara al consultor para interactuar eficazmente con el cliente, comunicando de forma clara y estratégica los hallazgos del análisis.", "categoria": "competencias_tecnicas", "factor": "Bloque 5: Presentación de ideas", "group": "Presentación de ideas", "source": "Signature", "role": "Consultor", "expert_behaviour": ["Prepara explicaciones claras para cada slide o mensaje, anticipando posibles preguntas del cliente.", "Practica con el equipo cómo comunicar los mensajes clave del módulo.", "Propone recomendaciones o implicaciones estratégicas derivadas del análisis.", "Participa en la presentación al cliente cuando se le asigna, elevando con claridad y seguridad."], "talented_behaviour": ["Anticipa con precisión las preocupaciones y objeciones de diferentes stakeholders, preparando respuestas convincentes.", "Transmite mensajes complejos con extraordinaria claridad y convicción, adaptando perfectamente el estilo a la audiencia.", "Maneja preguntas difíciles o inesperadas con soltura, transformando potenciales obstáculos en oportunidades persuasivas."], "low_skill_behaviour": ["Se prepara insuficientemente para presentaciones, resultando en explicaciones confusas o respuestas inadecuadas.", "Comunica de forma monótona o excesivamente técnica, sin adaptar el mensaje a las necesidades de la audiencia.", "Muestra inseguridad o defensividad cuando se cuestionan sus análisis o conclusiones."], "introducción_al_factor": "¿Qué es exactamente Presenting Your Ideas?\nEs la fase en la que el consultor convierte los resultados del análisis en mensajes estructurados y comprensibles, conectados con la lógica del proyecto y enfocados a decisiones. Aunque no siempre lidera la presentación final, sí tiene un rol clave en estructurar el storyline, construir slides con sentido y elevar implicaciones al cliente."}]