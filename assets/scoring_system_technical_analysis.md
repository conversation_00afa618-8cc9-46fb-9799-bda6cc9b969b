# Scoring System Architecture - Technical Analysis

## 1. Competency Categories & Behavior Types

### Database Schema
```sql
-- CompetencyCategory Enum (models.py:81-86)
FEEDBACK_CLIENTE = "feedback_cliente"
FEEDBACK_MANAGER = "feedback_manager" 
COMPETENCIAS_TECNICAS = "competencias_tecnicas"
COMPETENCIAS_COMPORTAMENTALES = "competencias_comportamentales"
APRENDIZAJE = "aprendizaje"

-- BehaviorType Enum (models.py:168-172)
EXPERT = "expert"      -- Standard performance behaviors
TALENTED = "talented"  -- High-value performance behaviors  
LOW_SKILL = "low_skill" -- Negative behaviors to minimize
```

### Competency Structure
- **Competency Table**: `id`, `code`, `name`, `description`, `factor`, `group`, `category`
- **Factor Grouping**: Competencies within categories are grouped by `factor` field (e.g., "Bloque 0", "Bloque 1")
- **Category Mapping**: Each competency belongs to one of five predefined categories

## 2. Role-Competency Mapping

### Implementation
- **CompetencyRoleMap Table**: Links roles to competencies with optional weights
- **Mapping Logic**: JSON configuration file (`assets/role_comp_map.json`) defines competency assignments per role
- **Weight System**: Default weight of 1.0 for all role-competency mappings
- **Seeding Process**: `seed_data.py:195-222` populates mappings from JSON configuration

### Current Role Mappings
```json
{
  "Analista": ["22", "29", "30", "31", "150-167", "35", "6", "7", "1"],
  "Consultor": ["22", "29", "30", "31", "168-185", "35", "6", "7", "1"]
}
```

## 3. Evaluation Matrix

### Database Structure
```sql
-- Evaluation Table (models.py:134-162)
evaluator_id: FK to User (who performs evaluation)
evaluatee_id: FK to User (who is being evaluated)
evaluator_type: ENUM(SELF, PEER) 
evaluation_type: ENUM(PERFORMANCE, POTENTIAL)
evaluatee_role_id: FK to Role (evaluatee's role at evaluation time)
```

### Evaluation Flow
- **Self-Evaluations**: `evaluator_id = evaluatee_id`, `evaluator_type = SELF`
- **Peer Evaluations**: `evaluator_id ≠ evaluatee_id`, `evaluator_type = PEER`
- **Role Validation**: System ensures evaluations use competencies mapped to `evaluatee_role_id`
- **Multi-Evaluator Support**: Multiple evaluators can evaluate the same evaluatee

## 4. Response Scoring Logic

### Three-Button System
```sql
-- ResponseType Enum (models.py:174-178)
SI = "si"           -- Sí (Yes)
NO = "no"           -- No  
A_VECES = "a_veces" -- A veces (Sometimes)
```

### Scoring Algorithms (score_calculator.py:87-109)
```python
# EXPERT Behaviors (Standard Performance)
if behavior_type == BehaviorType.EXPERT:
    if response == ResponseType.SI: points += 2
    elif response == ResponseType.A_VECES: points += 1
    # NO = 0 points
    max_possible_points += 2

# TALENTED Behaviors (High-Value Performance)  
elif behavior_type == BehaviorType.TALENTED:
    if response == ResponseType.SI: points += 3
    elif response == ResponseType.A_VECES: points += 1.5
    # NO = 0 points
    max_possible_points += 3

# LOW_SKILL Behaviors (Negative Behaviors)
elif behavior_type == BehaviorType.LOW_SKILL:
    if response == ResponseType.SI: points += -2
    elif response == ResponseType.A_VECES: points += -1
    # NO = 0 points (ideal for negative behaviors)
    max_possible_points += 0  # No positive contribution
```

### Final Score Calculation
```python
# Competency Score Formula (score_calculator.py:113-116)
score = (total_points / total_possible_points) * 100 if total_possible_points > 0 else 0
score = max(0, min(100, score))  # Clamp to 0-100 range
```

## 5. Score Aggregation Hierarchy

### Level 1: Competency Scores
- **Input**: Individual question responses per competency
- **Calculation**: Behavior-type weighted scoring as defined above
- **Output**: Single score (0-100) per competency per evaluation

### Level 2: Factor Scores  
```python
# Factor Score Calculation (score_calculator.py:144-163)
factor_score = sum(competency_scores) / len(competency_scores)
# Simple average of all competency scores within the factor
```

### Level 3: Category Scores
```python
# Category Score Calculation (score_calculator.py:179-197)  
category_score = sum(competency_scores) / len(competency_scores)
# Direct average from competencies, bypassing factors
```

### Mathematical Hierarchy
```
Individual Responses → Competency Scores → Factor Scores
                    ↘                   ↗
                      Category Scores
```

## 6. Weighted Scoring Implementation

### Role-Based Weights (score_calculator_service.py:19-25)
```python
DEFAULT_ROLE_WEIGHTS = {
    "Socio": 0.35,      # Senior Partner - highest weight
    "Director": 0.25,   # Director level
    "Manager": 0.20,    # Manager level  
    "Consultor": 0.15,  # Consultant level
    "Analista": 0.05    # Analyst - lowest weight
}
```

### Category-Based Weights (score_calculator_service.py:27-34)
```python
DEFAULT_CATEGORY_WEIGHTS = {
    "feedback_cliente": 0.25,              # Client feedback
    "feedback_manager": 0.25,              # Manager feedback
    "competencias_tecnicas": 0.20,         # Technical competencies
    "competencias_comportamentales": 0.20, # Behavioral competencies  
    "aprendizaje": 0.10                    # Learning competencies
}
```

### Weighted Score Formula (score_calculator_service.py:374-481)
```python
# Step 1: Calculate role-weighted scores per category
for category in categories:
    category_weighted_score = 0
    category_total_weight = 0
    
    for role in available_roles:
        role_weight = effective_role_weights[role]
        category_score = scores_by_category[category][role]["score"]
        
        category_weighted_score += category_score * role_weight
        category_total_weight += role_weight
    
    category_final_score = category_weighted_score / category_total_weight

# Step 2: Apply category weights to final calculation  
final_score = sum(category_final_score * category_weight for category, category_weight in effective_category_weights.items())
```

## 7. Evaluator Type Weighting Analysis

### Current Implementation Status
- **No Evaluator Type Differentiation**: The system currently groups data by `category` and `role` only
- **Code Evidence**: `score_calculator_service.py:388-389` explicitly states "without considering evaluator_type"
- **Database Support**: `EvaluatorType` enum exists but is not used in weighted calculations

### Technical Recommendation
**Implement evaluator type weighting** to differentiate self-evaluations from peer/manager evaluations:

```python
# Proposed Enhancement
EVALUATOR_TYPE_WEIGHTS = {
    "SELF": 0.3,   # Lower weight for self-assessments
    "PEER": 0.7    # Higher weight for peer/manager assessments  
}

# Modified calculation would include:
weighted_score += score * role_weight * evaluator_type_weight
```

### Business Justification
- **Self-Assessment Bias**: Research shows self-evaluations tend toward inflation
- **External Validation**: Peer/manager assessments provide more objective perspective
- **Balanced Scoring**: Weighted approach maintains self-reflection value while prioritizing external validation
