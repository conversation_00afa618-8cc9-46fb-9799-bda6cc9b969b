server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache";
    }

    location /assets {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }

    location /_event {
        proxy_pass http://reflex:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    location /_upload {
        proxy_pass http://reflex:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        client_max_body_size 10M;
    }

    location /_api {
        proxy_pass http://reflex:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
    }
} 