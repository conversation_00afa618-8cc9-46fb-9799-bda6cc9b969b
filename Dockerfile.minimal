FROM python:3.11-slim

WORKDIR /app

# Minimal system packages
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install only essential dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir reflex sqlmodel psycopg2-binary redis openai aiohttp python-dotenv msal

# Copy application code
COPY . .

# Minimal environment setup
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Initialize Reflex with minimal resources
RUN reflex init

# Expose only necessary port
EXPOSE 3000

# Simple healthcheck
HEALTHCHECK --interval=60s --timeout=30s --start-period=180s --retries=2 \
    CMD curl -f http://localhost:3000/ || exit 1

# Start command
CMD ["reflex", "run", "--env", "prod", "--loglevel", "info"]
