#!/bin/bash

echo "🚀 Starting Reflex-Chat Application"
echo "=================================="

# Print environment information
echo "📊 Environment Information:"
echo "RAILWAY_ENVIRONMENT_NAME: $RAILWAY_ENVIRONMENT_NAME"
echo "BASE_URL: $BASE_URL"
echo "FRONTEND_PORT: $FRONTEND_PORT"
echo "BACKEND_PORT: $BACKEND_PORT"
echo "HOST: $HOST"
echo "DATABASE_URL: ${DATABASE_URL:0:30}..." # Only show first 30 chars for security

# Print network information
echo ""
echo "🌐 Network Configuration:"
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -i 2>/dev/null || echo 'N/A')"

# Check if ports are available
echo ""
echo "🔍 Port Availability Check:"
if command -v netstat >/dev/null 2>&1; then
    echo "Checking port 3000..."
    netstat -ln | grep :3000 || echo "Port 3000 is available"
    echo "Checking port 8000..."
    netstat -ln | grep :8000 || echo "Port 8000 is available"
else
    echo "netstat not available, skipping port check"
fi

# Test database connectivity and operations
echo ""
echo "🗄️ Database Connectivity Test:"
if [ -n "$DATABASE_URL" ]; then
    echo "Database URL is configured"
    echo "Testing database operations..."
    python3 -c "
try:
    from Reflex_Chat.database.db import engine, create_db_and_tables
    from sqlalchemy import text
    print('✅ Database imports successful')

    # Test basic connection with proper SQLAlchemy 2.x syntax
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('✅ Database connection test passed')

    # Test table creation (this might be where it fails)
    create_db_and_tables()
    print('✅ Database tables created/verified')

except Exception as e:
    print(f'❌ Database error: {e}')
    import traceback
    traceback.print_exc()
"
else
    echo "⚠️ DATABASE_URL not set!"
fi

# Test Python imports
echo ""
echo "🐍 Python Import Test:"
python3 -c "
try:
    import reflex as rx
    print('✅ Reflex import successful')
    import Reflex_Chat
    print('✅ Reflex_Chat import successful')
    from Reflex_Chat.state import AuthState
    print('✅ AuthState import successful')
except Exception as e:
    print(f'❌ Import error: {e}')
    exit(1)
"

# Start Reflex with detailed logging and proper host binding
echo ""
echo "🎯 Starting Reflex Application..."
echo "Command: reflex run --env prod --backend-host 0.0.0.0 --loglevel debug"
echo ""

# Try simplified startup first, then fall back to debug mode
echo "🔄 Attempting simplified startup..."
reflex run --env prod --backend-host 0.0.0.0 &
REFLEX_PID=$!

# Wait for initial process startup
sleep 10
if kill -0 $REFLEX_PID 2>/dev/null; then
    echo "✅ Reflex started successfully (PID: $REFLEX_PID)"

    # Wait for production build to complete (up to 90 seconds)
    echo "🔍 Waiting for production build to complete..."
    for i in {1..18}; do
        if curl -f http://localhost:3000/ >/dev/null 2>&1; then
            echo "✅ Web server responding on port 3000! (after ${i}0 seconds)"
            break
        else
            echo "⏳ Build in progress... (${i}0s elapsed)"
            sleep 5
        fi
    done

    # Final check
    if curl -f http://localhost:3000/ >/dev/null 2>&1; then
        echo "🎉 Application fully ready!"
    else
        echo "⚠️ Web server still not responding after 90 seconds"
    fi

    wait $REFLEX_PID
else
    echo "❌ Reflex startup failed, trying with debug logging..."
    exec reflex run --env prod --backend-host 0.0.0.0 --loglevel debug
fi
