#!/bin/bash

echo "🚀 Starting Reflex-Chat Application"
echo "=================================="

# Print environment information
echo "📊 Environment Information:"
echo "RAILWAY_ENVIRONMENT_NAME: $RAILWAY_ENVIRONMENT_NAME"
echo "BASE_URL: $BASE_URL"
echo "FRONTEND_PORT: $FRONTEND_PORT"
echo "BACKEND_PORT: $BACKEND_PORT"
echo "HOST: $HOST"
echo "DATABASE_URL: ${DATABASE_URL:0:30}..." # Only show first 30 chars for security

# Print network information
echo ""
echo "🌐 Network Configuration:"
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -i 2>/dev/null || echo 'N/A')"

# Check if ports are available
echo ""
echo "🔍 Port Availability Check:"
if command -v netstat >/dev/null 2>&1; then
    echo "Checking port 3000..."
    netstat -ln | grep :3000 || echo "Port 3000 is available"
    echo "Checking port 8000..."
    netstat -ln | grep :8000 || echo "Port 8000 is available"
else
    echo "netstat not available, skipping port check"
fi

# Test database connectivity
echo ""
echo "🗄️ Database Connectivity Test:"
if [ -n "$DATABASE_URL" ]; then
    echo "Database URL is configured"
    # You could add a simple database connection test here
else
    echo "⚠️ DATABASE_URL not set!"
fi

# Start Reflex with detailed logging
echo ""
echo "🎯 Starting Reflex Application..."
echo "Command: reflex run --env prod --loglevel debug"
echo ""

# Execute Reflex
exec reflex run --env prod --loglevel debug
